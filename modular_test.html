<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块化广告屏蔽系统 - 测试验证页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .test-card.ad-js {
            border-color: #3498db;
        }
        
        .test-card.yt-ad {
            border-color: #e74c3c;
        }
        
        .test-card.bili-ad {
            border-color: #00a1d6;
        }
        
        .test-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #95a5a6;
            transition: all 0.3s ease;
        }
        
        .status-indicator.active {
            background: #27ae60;
            box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
        }
        
        .status-indicator.error {
            background: #e74c3c;
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
        }
        
        .test-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #3498db;
        }
        
        .test-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .results {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.8;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .results h3 {
            color: #3498db;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 8px 0;
            border-bottom: 1px solid #34495e;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            color: #27ae60;
            font-weight: bold;
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .icon {
            font-size: 1.5em;
            margin-right: 8px;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .testing {
            animation: pulse 1.5s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ 模块化广告屏蔽系统</h1>
            <p>测试验证页面 - 确保所有模块正常工作</p>
        </div>
        
        <div class="test-grid">
            <!-- ad.js 测试卡片 -->
            <div class="test-card ad-js">
                <h3>
                    <span class="icon">🌐</span>
                    通用广告屏蔽器
                    <span class="status-indicator" id="ad-status"></span>
                </h3>
                <div class="test-info">
                    <strong>脚本:</strong> ad.js<br>
                    <strong>目标:</strong> 所有网站（除YouTube、B站）<br>
                    <strong>功能:</strong> 通用广告过滤、社交媒体优化
                </div>
                <button class="test-button" onclick="testAdJS()">测试通用屏蔽</button>
                <button class="test-button" onclick="checkAdJSStatus()">检查状态</button>
            </div>
            
            <!-- yt_ad.js 测试卡片 -->
            <div class="test-card yt-ad">
                <h3>
                    <span class="icon">🎬</span>
                    YouTube专用屏蔽器
                    <span class="status-indicator" id="yt-status"></span>
                </h3>
                <div class="test-info">
                    <strong>脚本:</strong> yt_ad.js<br>
                    <strong>目标:</strong> YouTube.com<br>
                    <strong>功能:</strong> 视频广告跳过、反检测
                </div>
                <button class="test-button" onclick="testYouTube()">测试YouTube</button>
                <button class="test-button" onclick="checkYTStatus()">检查状态</button>
            </div>
            
            <!-- bili_ad.js 测试卡片 -->
            <div class="test-card bili-ad">
                <h3>
                    <span class="icon">📺</span>
                    B站专用屏蔽器
                    <span class="status-indicator" id="bili-status"></span>
                </h3>
                <div class="test-info">
                    <strong>脚本:</strong> bili_ad.js<br>
                    <strong>目标:</strong> Bilibili.com<br>
                    <strong>功能:</strong> UI保护、精确广告屏蔽
                </div>
                <button class="test-button" onclick="testBilibili()">测试B站</button>
                <button class="test-button" onclick="checkBiliStatus()">检查状态</button>
            </div>
        </div>
        
        <div class="controls">
            <button class="test-button" onclick="runFullTest()">🚀 运行完整测试</button>
            <button class="test-button" onclick="checkAllStatus()">📊 检查所有状态</button>
            <button class="test-button" onclick="clearResults()">🗑️ 清除结果</button>
        </div>
        
        <div class="results" id="results">
            <h3>📋 测试结果</h3>
            <div id="results-content">
                点击上方按钮开始测试...
            </div>
        </div>
    </div>
    
    <script>
        // 测试结果存储
        let testResults = {
            adJS: { status: 'unknown', details: {} },
            ytAd: { status: 'unknown', details: {} },
            biliAd: { status: 'unknown', details: {} }
        };
        
        // 更新状态指示器
        function updateStatusIndicator(id, status) {
            const indicator = document.getElementById(id);
            indicator.className = 'status-indicator';
            if (status === 'active') {
                indicator.classList.add('active');
            } else if (status === 'error') {
                indicator.classList.add('error');
            }
        }
        
        // 添加结果到显示区域
        function addResult(message, type = 'info') {
            const resultsContent = document.getElementById('results-content');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db';
            
            resultsContent.innerHTML += `
                <div style="color: ${color}; margin: 5px 0;">
                    [${timestamp}] ${message}
                </div>
            `;
            
            // 自动滚动到底部
            const results = document.getElementById('results');
            results.scrollTop = results.scrollHeight;
        }
        
        // 测试 ad.js
        function testAdJS() {
            addResult('🌐 开始测试通用广告屏蔽器...');
            
            // 检查是否存在通用广告屏蔽器的API
            if (window.UAB_AdBlocker) {
                const stats = window.UAB_AdBlocker.getStats();
                testResults.adJS.status = 'active';
                testResults.adJS.details = stats;
                
                updateStatusIndicator('ad-status', 'active');
                addResult(`✅ ad.js 运行正常`, 'success');
                addResult(`📊 屏蔽元素: ${stats.elementsBlocked || 0}`, 'info');
                addResult(`🌐 拦截请求: ${stats.requestsBlocked || 0}`, 'info');
            } else {
                testResults.adJS.status = 'error';
                updateStatusIndicator('ad-status', 'error');
                addResult(`❌ ad.js 未检测到或未启用`, 'error');
            }
        }
        
        // 检查 ad.js 状态
        function checkAdJSStatus() {
            addResult('🔍 检查通用广告屏蔽器状态...');
            
            // 检查域名排除
            const hostname = window.location.hostname;
            if (hostname.includes('youtube.com') || hostname.includes('bilibili.com')) {
                addResult(`ℹ️ 当前域名 ${hostname} 应该被 ad.js 排除`, 'info');
                return;
            }
            
            // 检查CSS样式注入
            const adBlockStyles = document.getElementById('UAB_ad-block-styles');
            if (adBlockStyles) {
                addResult(`✅ CSS样式已注入`, 'success');
            } else {
                addResult(`⚠️ 未检测到CSS样式注入`, 'error');
            }
        }
        
        // 测试 YouTube
        function testYouTube() {
            addResult('🎬 开始测试YouTube专用屏蔽器...');
            
            if (window.location.hostname.includes('youtube.com')) {
                // 在YouTube页面上测试
                const ytElements = document.querySelectorAll('.ytp-ad-module, .video-ads');
                addResult(`📺 检测到 ${ytElements.length} 个YouTube广告元素`, 'info');
                
                // 检查跳过按钮处理
                const skipButtons = document.querySelectorAll('.ytp-ad-skip-button-modern, .ytp-ad-skip-button');
                addResult(`⏭️ 检测到 ${skipButtons.length} 个跳过按钮`, 'info');
                
                updateStatusIndicator('yt-status', 'active');
                addResult(`✅ YouTube环境检测完成`, 'success');
            } else {
                addResult(`ℹ️ 当前不在YouTube页面，建议访问 youtube.com 进行测试`, 'info');
                updateStatusIndicator('yt-status', 'error');
            }
        }
        
        // 检查 YouTube 状态
        function checkYTStatus() {
            addResult('🔍 检查YouTube专用屏蔽器状态...');
            
            if (!window.location.hostname.includes('youtube.com')) {
                addResult(`ℹ️ 当前不在YouTube域名，yt_ad.js 不应在此运行`, 'info');
                return;
            }
            
            // 检查YouTube特定的处理
            const adShowing = document.querySelectorAll('.ad-showing');
            const videoAds = document.querySelectorAll('.video-ads');
            
            addResult(`📊 .ad-showing 元素: ${adShowing.length}`, 'info');
            addResult(`📊 .video-ads 元素: ${videoAds.length}`, 'info');
        }
        
        // 测试 B站
        function testBilibili() {
            addResult('📺 开始测试B站专用屏蔽器...');
            
            if (window.location.hostname.includes('bilibili.com')) {
                // 在B站页面上测试
                if (window.BILI_AdBlocker) {
                    const stats = window.BILI_AdBlocker.getStats();
                    testResults.biliAd.status = 'active';
                    testResults.biliAd.details = stats;
                    
                    updateStatusIndicator('bili-status', 'active');
                    addResult(`✅ bili_ad.js 运行正常`, 'success');
                    addResult(`📊 屏蔽广告: ${stats.adsBlocked || 0}`, 'info');
                    addResult(`🛡️ 保护元素: ${stats.elementsProtected || 0}`, 'info');
                } else {
                    updateStatusIndicator('bili-status', 'error');
                    addResult(`❌ bili_ad.js 未检测到或未启用`, 'error');
                }
                
                // 检查UI元素保护
                const videoCards = document.querySelectorAll('.video-card, .bili-video-card');
                const banners = document.querySelectorAll('.banner:not(.ad-banner)');
                
                addResult(`🎴 视频卡片: ${videoCards.length}`, 'info');
                addResult(`🖼️ 正常横幅: ${banners.length}`, 'info');
            } else {
                addResult(`ℹ️ 当前不在B站页面，建议访问 bilibili.com 进行测试`, 'info');
                updateStatusIndicator('bili-status', 'error');
            }
        }
        
        // 检查 B站状态
        function checkBiliStatus() {
            addResult('🔍 检查B站专用屏蔽器状态...');
            
            if (!window.location.hostname.includes('bilibili.com')) {
                addResult(`ℹ️ 当前不在B站域名，bili_ad.js 不应在此运行`, 'info');
                return;
            }
            
            // 检查B站特定的CSS注入
            const biliStyles = document.getElementById('BILI_ad-block-styles');
            if (biliStyles) {
                addResult(`✅ B站专用CSS样式已注入`, 'success');
            } else {
                addResult(`⚠️ 未检测到B站专用CSS样式`, 'error');
            }
        }
        
        // 运行完整测试
        function runFullTest() {
            addResult('🚀 开始运行完整测试套件...', 'success');
            addResult('=' * 50);
            
            setTimeout(() => {
                testAdJS();
                setTimeout(() => {
                    testYouTube();
                    setTimeout(() => {
                        testBilibili();
                        setTimeout(() => {
                            generateTestReport();
                        }, 1000);
                    }, 1000);
                }, 1000);
            }, 500);
        }
        
        // 检查所有状态
        function checkAllStatus() {
            addResult('📊 检查所有模块状态...', 'success');
            
            checkAdJSStatus();
            setTimeout(() => checkYTStatus(), 500);
            setTimeout(() => checkBiliStatus(), 1000);
        }
        
        // 生成测试报告
        function generateTestReport() {
            addResult('📋 生成测试报告...', 'success');
            addResult('=' * 50);
            
            const hostname = window.location.hostname;
            addResult(`🌐 当前域名: ${hostname}`, 'info');
            
            // 统计结果
            let activeModules = 0;
            let totalModules = 3;
            
            Object.values(testResults).forEach(result => {
                if (result.status === 'active') activeModules++;
            });
            
            addResult(`📊 模块状态: ${activeModules}/${totalModules} 活跃`, 'info');
            addResult(`⚡ 系统性能: ${activeModules > 0 ? '正常' : '需要检查'}`, 
                     activeModules > 0 ? 'success' : 'error');
            
            // 建议
            if (hostname.includes('youtube.com') && testResults.ytAd.status !== 'active') {
                addResult(`💡 建议: 在YouTube页面安装并启用 yt_ad.js`, 'info');
            }
            
            if (hostname.includes('bilibili.com') && testResults.biliAd.status !== 'active') {
                addResult(`💡 建议: 在B站页面安装并启用 bili_ad.js`, 'info');
            }
            
            if (!hostname.includes('youtube.com') && !hostname.includes('bilibili.com') && 
                testResults.adJS.status !== 'active') {
                addResult(`💡 建议: 在通用网站安装并启用 ad.js`, 'info');
            }
            
            addResult('✅ 测试报告生成完成', 'success');
        }
        
        // 清除结果
        function clearResults() {
            document.getElementById('results-content').innerHTML = '点击上方按钮开始测试...';
            
            // 重置状态指示器
            ['ad-status', 'yt-status', 'bili-status'].forEach(id => {
                updateStatusIndicator(id, 'unknown');
            });
            
            // 重置测试结果
            testResults = {
                adJS: { status: 'unknown', details: {} },
                ytAd: { status: 'unknown', details: {} },
                biliAd: { status: 'unknown', details: {} }
            };
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('🎉 模块化广告屏蔽系统测试页面已加载', 'success');
                addResult(`🌐 当前域名: ${window.location.hostname}`, 'info');
                addResult('点击上方按钮开始测试各个模块...', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
