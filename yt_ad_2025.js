// ==UserScript==
// @name         YouTube Ad Blocker 2025 (Working Edition)
// @version      1.0
// @description  Working YouTube ad blocker based on 2025 best practices
// <AUTHOR> Assistant
// @match        *://www.youtube.com/*
// @match        *://*.youtube.com/*
// @run-at       document-start
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    
    // ===== 2025 CONFIGURATION =====
    const CONFIG = {
        DEBUG: false,
        SELECTORS: {
            // Updated 2025 selectors based on latest research
            SKIP_BUTTONS: [
                '.ytp-skip-ad-button',
                '.ytp-ad-skip-button',
                '.ytp-ad-skip-button-modern',
                '.ytp-skip-ad-button-modern',
                '[class*="skip"][class*="button"]',
                '[aria-label*="Skip"]',
                '[aria-label*="跳过"]'
            ],
            AD_CONTAINERS: [
                '.video-ads',
                '.ytp-ad-module',
                '.ytp-ad-overlay-container',
                '.ytp-ad-text-overlay',
                '.ytp-ad-player-overlay',
                '.ad-showing',
                '[class*="ad-showing"]',
                '.ytp-ad-image-overlay'
            ],
            AD_VIDEOS: [
                'video[src*="googlevideo.com/videoplayback"]',
                '.ad-showing video',
                '.video-ads video'
            ]
        },
        TIMING: {
            OBSERVER_THROTTLE: 50,
            SKIP_DELAY: 100,
            VIDEO_CHECK_INTERVAL: 200,
            MAX_RETRIES: 5
        }
    };

    // ===== LOGGING SYSTEM =====
    const log = (...args) => CONFIG.DEBUG && console.log('[YT-AdBlock-2025]', ...args);
    const warn = (...args) => CONFIG.DEBUG && console.warn('[YT-AdBlock-2025]', ...args);
    const error = (...args) => console.error('[YT-AdBlock-2025]', ...args);

    // ===== CORE AD BLOCKER CLASS =====
    class YouTubeAdBlocker2025 {
        constructor() {
            this.isActive = false;
            this.retryCount = 0;
            this.lastSkipTime = 0;
            this.processedElements = new WeakSet();
            this.observer = null;
            this.videoCheckInterval = null;
            
            this.init();
        }
        
        init() {
            log('Initializing YouTube Ad Blocker 2025...');
            
            // Wait for page to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.start());
            } else {
                this.start();
            }
            
            // Backup start
            setTimeout(() => this.start(), 1000);
        }
        
        start() {
            if (this.isActive) return;
            this.isActive = true;
            
            log('Starting ad blocking...');
            
            // Inject CSS immediately
            this.injectCSS();
            
            // Setup observers
            this.setupMutationObserver();
            this.setupVideoMonitor();
            
            // Initial scan
            this.scanAndProcess();
            
            log('Ad blocker activated');
        }
        
        injectCSS() {
            if (document.getElementById('yt-adblock-2025-styles')) return;
            
            const style = document.createElement('style');
            style.id = 'yt-adblock-2025-styles';
            style.textContent = `
                /* Hide ad containers */
                .video-ads,
                .ytp-ad-module,
                .ytp-ad-overlay-container,
                .ytp-ad-text-overlay,
                .ytp-ad-player-overlay,
                .ytp-ad-image-overlay,
                .ad-showing .ytp-ad-module,
                [class*="ad-showing"] .ytp-ad-module {
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    height: 0 !important;
                    width: 0 !important;
                }
                
                /* Ensure video player remains visible */
                .html5-video-player:not(.ad-showing) video,
                .html5-video-player video:not([src*="googlevideo.com/videoplayback"]) {
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                }
            `;
            
            (document.head || document.documentElement).appendChild(style);
            log('CSS injected');
        }
        
        setupMutationObserver() {
            if (this.observer) return;
            
            this.observer = new MutationObserver((mutations) => {
                let shouldProcess = false;
                
                for (const mutation of mutations) {
                    if (mutation.addedNodes.length > 0) {
                        for (const node of mutation.addedNodes) {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                // Check for ad-related elements
                                if (this.isAdRelated(node)) {
                                    shouldProcess = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (shouldProcess) break;
                }
                
                if (shouldProcess) {
                    this.throttledProcess();
                }
            });
            
            this.observer.observe(document.documentElement, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'src']
            });
            
            log('Mutation observer setup');
        }
        
        setupVideoMonitor() {
            if (this.videoCheckInterval) return;
            
            this.videoCheckInterval = setInterval(() => {
                this.checkForAdVideos();
            }, CONFIG.TIMING.VIDEO_CHECK_INTERVAL);
            
            log('Video monitor setup');
        }
        
        isAdRelated(element) {
            if (!element.className && !element.id) return false;
            
            const className = element.className.toString().toLowerCase();
            const id = element.id.toLowerCase();
            
            return className.includes('ad-') || 
                   className.includes('ytp-ad') ||
                   id.includes('ad-') ||
                   element.tagName === 'VIDEO';
        }
        
        throttledProcess = (() => {
            let timeout;
            return () => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.scanAndProcess();
                }, CONFIG.TIMING.OBSERVER_THROTTLE);
            };
        })();
        
        scanAndProcess() {
            try {
                this.handleSkipButtons();
                this.handleAdVideos();
                this.hideAdContainers();
                this.retryCount = 0; // Reset on success
            } catch (err) {
                error('Error in scanAndProcess:', err);
                this.retryCount++;
                
                if (this.retryCount < CONFIG.TIMING.MAX_RETRIES) {
                    setTimeout(() => this.scanAndProcess(), 1000);
                }
            }
        }
        
        handleSkipButtons() {
            const now = Date.now();
            if (now - this.lastSkipTime < CONFIG.TIMING.SKIP_DELAY) return;
            
            for (const selector of CONFIG.SELECTORS.SKIP_BUTTONS) {
                const buttons = document.querySelectorAll(selector);
                
                for (const button of buttons) {
                    if (this.processedElements.has(button)) continue;
                    
                    if (this.isElementVisible(button)) {
                        this.processedElements.add(button);
                        
                        // Use multiple click strategies
                        setTimeout(() => {
                            this.clickElement(button);
                            this.lastSkipTime = now;
                            log('Skip button clicked:', selector);
                        }, 50);
                        
                        return; // Only click one button at a time
                    }
                }
            }
        }
        
        handleAdVideos() {
            for (const selector of CONFIG.SELECTORS.AD_VIDEOS) {
                const videos = document.querySelectorAll(selector);
                
                for (const video of videos) {
                    if (this.processedElements.has(video)) continue;
                    
                    this.processedElements.add(video);
                    this.processAdVideo(video);
                }
            }
        }
        
        processAdVideo(video) {
            try {
                // Multiple strategies for ad video handling
                video.muted = true;
                video.volume = 0;
                
                if (video.duration && !isNaN(video.duration) && video.duration > 0) {
                    // Strategy 1: Jump to end
                    video.currentTime = video.duration - 0.1;
                    
                    // Strategy 2: Trigger ended event
                    setTimeout(() => {
                        const endedEvent = new Event('ended');
                        video.dispatchEvent(endedEvent);
                    }, 100);
                    
                    // Strategy 3: Speed up
                    video.playbackRate = 16;
                }
                
                log('Ad video processed');
            } catch (err) {
                warn('Error processing ad video:', err);
            }
        }
        
        hideAdContainers() {
            for (const selector of CONFIG.SELECTORS.AD_CONTAINERS) {
                const containers = document.querySelectorAll(selector);
                
                for (const container of containers) {
                    if (this.processedElements.has(container)) continue;
                    
                    this.processedElements.add(container);
                    this.hideElement(container);
                }
            }
        }
        
        checkForAdVideos() {
            // Check for ad-showing class on player
            const player = document.querySelector('.html5-video-player');
            if (player && player.classList.contains('ad-showing')) {
                this.handleSkipButtons();
                
                // Force skip if ad is stuck
                const video = player.querySelector('video');
                if (video && video.currentTime > 0) {
                    this.processAdVideo(video);
                }
            }
        }
        
        isElementVisible(element) {
            if (!element.offsetParent) return false;
            
            const rect = element.getBoundingClientRect();
            return rect.width > 0 && rect.height > 0;
        }
        
        clickElement(element) {
            // Multiple click strategies for better compatibility
            try {
                // Strategy 1: Direct click
                element.click();
                
                // Strategy 2: Mouse events
                const events = ['mousedown', 'mouseup', 'click'];
                events.forEach(eventType => {
                    const event = new MouseEvent(eventType, {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    element.dispatchEvent(event);
                });
                
                // Strategy 3: Focus and Enter
                element.focus();
                const enterEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    bubbles: true
                });
                element.dispatchEvent(enterEvent);
                
            } catch (err) {
                warn('Error clicking element:', err);
            }
        }
        
        hideElement(element) {
            try {
                element.style.setProperty('display', 'none', 'important');
                element.style.setProperty('visibility', 'hidden', 'important');
                element.style.setProperty('opacity', '0', 'important');
                element.style.setProperty('height', '0', 'important');
                element.style.setProperty('width', '0', 'important');
            } catch (err) {
                warn('Error hiding element:', err);
            }
        }
        
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
            
            if (this.videoCheckInterval) {
                clearInterval(this.videoCheckInterval);
                this.videoCheckInterval = null;
            }
            
            this.isActive = false;
            log('Ad blocker destroyed');
        }
    }

    // ===== INITIALIZATION =====
    let adBlocker = null;
    
    // Start immediately
    adBlocker = new YouTubeAdBlocker2025();
    
    // Handle page navigation (YouTube SPA)
    let currentUrl = location.href;
    const urlObserver = new MutationObserver(() => {
        if (location.href !== currentUrl) {
            currentUrl = location.href;
            log('URL changed, reinitializing...');
            
            if (adBlocker) {
                adBlocker.destroy();
            }
            
            setTimeout(() => {
                adBlocker = new YouTubeAdBlocker2025();
            }, 500);
        }
    });
    
    urlObserver.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    // Global error handling
    window.addEventListener('error', (event) => {
        if (event.error && event.error.stack && event.error.stack.includes('YT-AdBlock-2025')) {
            error('Global error:', event.error);
        }
    });
    
    log('YouTube Ad Blocker 2025 loaded');

})();
