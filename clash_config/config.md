# 2025年mihomo内核优化配置 - 适用于Clash Verge Rev
# 兼容中国大陆网络环境的广告拦截和分流规则

# 规则提供者配置 - 使用2025年最新规则集
rule-providers:
  # Loyalsoldier规则集 - 基础分流
  reject:
    type: http
    behavior: domain
    url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt"
    path: ./ruleset/loyalsoldier/reject.yaml
    interval: 86400

  gfw:
    type: http
    behavior: domain
    url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/gfw.txt"
    path: ./ruleset/loyalsoldier/gfw.yaml
    interval: 86400

  proxy:
    type: http
    behavior: domain
    url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt"
    path: ./ruleset/loyalsoldier/proxy.yaml
    interval: 86400

  direct:
    type: http
    behavior: domain
    url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/direct.txt"
    path: ./ruleset/loyalsoldier/direct.yaml
    interval: 86400

  # BlackMatrix7规则集 - 精细化分流
  advertising:
    type: http
    behavior: classical
    url: "https://fastly.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Advertising/Advertising.yaml"
    path: ./ruleset/blackmatrix7/advertising.yaml
    interval: 86400

  youtube:
    type: http
    behavior: classical
    url: "https://fastly.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/YouTube/YouTube.yaml"
    path: ./ruleset/blackmatrix7/youtube.yaml
    interval: 86400

  spotify:
    type: http
    behavior: classical
    url: "https://fastly.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Spotify/Spotify.yaml"
    path: ./ruleset/blackmatrix7/spotify.yaml
    interval: 86400

  openai:
    type: http
    behavior: classical
    url: "https://fastly.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/OpenAI/OpenAI.yaml"
    path: ./ruleset/blackmatrix7/openai.yaml
    interval: 86400

  telegram:
    type: http
    behavior: classical
    url: "https://fastly.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Telegram/Telegram.yaml"
    path: ./ruleset/blackmatrix7/telegram.yaml
    interval: 86400

# 代理组配置
proxy-groups:
  - name: "🚀 节点选择"
    type: select
    proxies:
      - "♻️ 自动选择"
      - "🔯 故障转移"
      - "🔮 负载均衡"
      - "🎯 全球直连"
    include-all: true

  - name: "♻️ 自动选择"
    type: url-test
    include-all: true
    url: "https://www.google.com/generate_204"
    interval: 300
    tolerance: 50

  - name: "🔯 故障转移"
    type: fallback
    include-all: true
    url: "https://www.google.com/generate_204"
    interval: 300

  - name: "🔮 负载均衡"
    type: load-balance
    strategy: consistent-hashing
    include-all: true
    url: "https://www.google.com/generate_204"
    interval: 300

  - name: "🎵 Spotify"
    type: select
    proxies:
      - "🚀 节点选择"
      - "♻️ 自动选择"
      - "🎯 全球直连"
    include-all: true

  - name: "📺 YouTube"
    type: select
    proxies:
      - "🚀 节点选择"
      - "♻️ 自动选择"
    include-all: true

  - name: "🤖 OpenAI"
    type: select
    proxies:
      - "🚀 节点选择"
      - "♻️ 自动选择"
    include-all: true

  - name: "📲 Telegram"
    type: select
    proxies:
      - "🚀 节点选择"
      - "♻️ 自动选择"
    include-all: true

  - name: "🛑 广告拦截"
    type: select
    proxies:
      - "REJECT"
      - "DIRECT"

  - name: "🎯 全球直连"
    type: select
    proxies:
      - "DIRECT"
      - "🚀 节点选择"

  - name: "🐟 漏网之鱼"
    type: select
    proxies:
      - "🚀 节点选择"
      - "🎯 全球直连"

# 分流规则 - 2025年优化版本
rules:
  # 本地网络直连
  - GEOSITE,private,🎯 全球直连
  - GEOIP,private,🎯 全球直连,no-resolve

  # 广告拦截 - 优先级最高
  - RULE-SET,advertising,🛑 广告拦截
  - RULE-SET,reject,🛑 广告拦截
  - GEOSITE,category-ads-all,🛑 广告拦截

  # 2025年新增广告域名 - 手动维护
  - DOMAIN-SUFFIX,securepubads.g.doubleclick.net,🛑 广告拦截
  - DOMAIN-SUFFIX,pubads.g.doubleclick.net,🛑 广告拦截
  - DOMAIN-SUFFIX,googlesyndication.com,🛑 广告拦截
  - DOMAIN-SUFFIX,googleadservices.com,🛑 广告拦截
  - DOMAIN-SUFFIX,googletagmanager.com,🛑 广告拦截
  - DOMAIN-SUFFIX,google-analytics.com,🛑 广告拦截
  - DOMAIN-SUFFIX,admob.com,🛑 广告拦截
  - DOMAIN-SUFFIX,adsystem.google.com,🛑 广告拦截
  - DOMAIN-SUFFIX,cm.pos.baidu.com,🛑 广告拦截
  - DOMAIN-SUFFIX,cpro.baidu.com,🛑 广告拦截
  - DOMAIN-SUFFIX,ad.qq.com,🛑 广告拦截
  - DOMAIN-SUFFIX,ads.twitter.com,🛑 广告拦截
  - DOMAIN-SUFFIX,ads-api.twitter.com,🛑 广告拦截
  - DOMAIN-SUFFIX,analytics.twitter.com,🛑 广告拦截

  # Spotify广告拦截 - 2025年更新
  - DOMAIN-SUFFIX,aet.spotify.com,🛑 广告拦截
  - DOMAIN-SUFFIX,api-partner.spotify.com,🛑 广告拦截
  - DOMAIN-SUFFIX,ap-gae2.spotify.com,🛑 广告拦截
  - DOMAIN-SUFFIX,gae2-spclient.spotify.com,🛑 广告拦截
  - DOMAIN-SUFFIX,spclient.wg.spotify.com,🛑 广告拦截
  - DOMAIN-SUFFIX,gae2-dealer.g2.spotify.com,🛑 广告拦截
  - DOMAIN-SUFFIX,ads-fa.spotify.com,🛑 广告拦截
  - DOMAIN-SUFFIX,ads.spotify.com,🛑 广告拦截
  - DOMAIN-SUFFIX,analytics.spotify.com,🛑 广告拦截
  - DOMAIN-SUFFIX,cdn-spotify-experiments.conductrics.com,🛑 广告拦截
  - DOMAIN-SUFFIX,video-fa.scdn.co,🛑 广告拦截

  # 2025年新增第三方广告网络
  - DOMAIN-SUFFIX,rubiconproject.com,🛑 广告拦截
  - DOMAIN-SUFFIX,adnxs.com,🛑 广告拦截
  - DOMAIN-SUFFIX,adsystem.google,🛑 广告拦截
  - DOMAIN-SUFFIX,adtrafficquality.google,🛑 广告拦截
  - DOMAIN-SUFFIX,adclick.g.doubleclick.net,🛑 广告拦截
  - DOMAIN-SUFFIX,amazon-adsystem.com,🛑 广告拦截
  - DOMAIN-SUFFIX,facebook.com,🛑 广告拦截
  - DOMAIN-SUFFIX,criteo.com,🛑 广告拦截
  - DOMAIN-SUFFIX,outbrain.com,🛑 广告拦截
  - DOMAIN-SUFFIX,taboola.com,🛑 广告拦截

  # 应用分流
  - RULE-SET,openai,🤖 OpenAI
  - RULE-SET,telegram,📲 Telegram
  - RULE-SET,youtube,📺 YouTube
  - RULE-SET,spotify,🎵 Spotify

  # 国外服务
  - RULE-SET,gfw,🚀 节点选择
  - RULE-SET,proxy,🚀 节点选择
  - GEOSITE,geolocation-!cn,🚀 节点选择

  # 国内直连
  - RULE-SET,direct,🎯 全球直连
  - GEOSITE,cn,🎯 全球直连
  - GEOIP,cn,🎯 全球直连

  # 最终规则
  - MATCH,🐟 漏网之鱼
