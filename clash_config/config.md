# 2025年mihomo内核附加配置 - 适用于Clash Verge Rev
# ⚠️ 注意：此配置采用"附加"模式，不会覆盖机场原有配置
# 兼容中国大陆网络环境的广告拦截增强

# ⚠️ 冲突警告：rule-providers字段会与机场配置合并
# 如果机场已有同名规则集，会被此处覆盖
# 建议：如有冲突，请手动重命名下方规则集名称
rule-providers:
  # 附加广告拦截规则集 - 避免与机场冲突使用独特命名
  custom_ad_block_2025:
    type: http
    behavior: domain
    url: "https://fastly.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Advertising/Advertising.yaml"
    path: ./ruleset/custom/advertising_2025.yaml
    interval: 86400

  custom_privacy_block:
    type: http
    behavior: domain
    url: "https://fastly.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Privacy/Privacy.yaml"
    path: ./ruleset/custom/privacy_2025.yaml
    interval: 86400

  custom_hijacking_block:
    type: http
    behavior: domain
    url: "https://fastly.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Hijacking/Hijacking.yaml"
    path: ./ruleset/custom/hijacking_2025.yaml
    interval: 86400

# ⚠️ 严重冲突警告：proxy-groups字段会完全覆盖机场的策略组配置！
# 解决方案：移除此字段，改用JavaScript脚本动态添加策略组
# 如果您坚持使用，机场原有的所有策略组将被替换为下方配置
# proxy-groups:
#   - name: "🛑 广告拦截"
#     type: select
#     proxies:
#       - "REJECT"
#       - "DIRECT"

# ⚠️ 冲突警告：prepend-rules字段在新版本中已废弃，改用rules字段
# rules字段会与机场规则合并，但优先级可能不同
# 建议：使用JavaScript脚本动态插入规则到最前面
# ⚠️ 注意：以下规则将添加到机场规则的最前面（优先级最高）
# 如果机场已有相同的策略组名称，请手动修改下方策略组名称以避免冲突

# 2025年手动维护的广告域名 - 直接拦截规则
# 这些规则会插入到机场规则最前面，确保广告拦截优先级
rules:
  # Google广告生态系统 - 2025年更新
  - DOMAIN-SUFFIX,securepubads.g.doubleclick.net,REJECT
  - DOMAIN-SUFFIX,pubads.g.doubleclick.net,REJECT
  - DOMAIN-SUFFIX,googlesyndication.com,REJECT
  - DOMAIN-SUFFIX,googleadservices.com,REJECT
  - DOMAIN-SUFFIX,googletagmanager.com,REJECT
  - DOMAIN-SUFFIX,google-analytics.com,REJECT
  - DOMAIN-SUFFIX,admob.com,REJECT
  - DOMAIN-SUFFIX,adsystem.google.com,REJECT
  - DOMAIN-SUFFIX,adsystem.google,REJECT
  - DOMAIN-SUFFIX,adtrafficquality.google,REJECT
  - DOMAIN-SUFFIX,adclick.g.doubleclick.net,REJECT
  - DOMAIN-SUFFIX,googletag.com,REJECT
  - DOMAIN-SUFFIX,googletagservices.com,REJECT

  # 百度广告系统
  - DOMAIN-SUFFIX,cm.pos.baidu.com,REJECT
  - DOMAIN-SUFFIX,cpro.baidu.com,REJECT
  - DOMAIN-SUFFIX,pos.baidu.com,REJECT
  - DOMAIN-SUFFIX,eiv.baidu.com,REJECT
  - DOMAIN-SUFFIX,nsclick.baidu.com,REJECT

  # 腾讯广告系统
  - DOMAIN-SUFFIX,ad.qq.com,REJECT
  - DOMAIN-SUFFIX,ads.qq.com,REJECT
  - DOMAIN-SUFFIX,btrace.qq.com,REJECT
  - DOMAIN-SUFFIX,pingjs.qq.com,REJECT
  - DOMAIN-SUFFIX,pingma.qq.com,REJECT

  # 社交媒体广告
  - DOMAIN-SUFFIX,ads.twitter.com,REJECT
  - DOMAIN-SUFFIX,ads-api.twitter.com,REJECT
  - DOMAIN-SUFFIX,analytics.twitter.com,REJECT
  - DOMAIN-SUFFIX,ads.facebook.com,REJECT
  - DOMAIN-SUFFIX,an.facebook.com,REJECT
  - DOMAIN-SUFFIX,connect.facebook.net,REJECT

  # Spotify广告系统 - 2025年完整版
  - DOMAIN-SUFFIX,aet.spotify.com,REJECT
  - DOMAIN-SUFFIX,api-partner.spotify.com,REJECT
  - DOMAIN-SUFFIX,ap-gae2.spotify.com,REJECT
  - DOMAIN-SUFFIX,gae2-spclient.spotify.com,REJECT
  - DOMAIN-SUFFIX,spclient.wg.spotify.com,REJECT
  - DOMAIN-SUFFIX,gae2-dealer.g2.spotify.com,REJECT
  - DOMAIN-SUFFIX,ads-fa.spotify.com,REJECT
  - DOMAIN-SUFFIX,ads.spotify.com,REJECT
  - DOMAIN-SUFFIX,analytics.spotify.com,REJECT
  - DOMAIN-SUFFIX,cdn-spotify-experiments.conductrics.com,REJECT
  - DOMAIN-SUFFIX,video-fa.scdn.co,REJECT

  # 第三方广告网络 - 2025年新增
  - DOMAIN-SUFFIX,rubiconproject.com,REJECT
  - DOMAIN-SUFFIX,adnxs.com,REJECT
  - DOMAIN-SUFFIX,amazon-adsystem.com,REJECT
  - DOMAIN-SUFFIX,criteo.com,REJECT
  - DOMAIN-SUFFIX,outbrain.com,REJECT
  - DOMAIN-SUFFIX,taboola.com,REJECT
  - DOMAIN-SUFFIX,adsystem.amazon.com,REJECT

  # 移动广告平台
  - DOMAIN-SUFFIX,unity3d.com,REJECT
  - DOMAIN-SUFFIX,unityads.unity3d.com,REJECT
  - DOMAIN-SUFFIX,applifier.com,REJECT
  - DOMAIN-SUFFIX,chartboost.com,REJECT
  - DOMAIN-SUFFIX,vungle.com,REJECT
  - DOMAIN-SUFFIX,applovin.com,REJECT
  - DOMAIN-SUFFIX,ironsrc.com,REJECT

  # 国内广告平台
  - DOMAIN-SUFFIX,tanx.com,REJECT
  - DOMAIN-SUFFIX,alimama.com,REJECT
  - DOMAIN-SUFFIX,mmstat.com,REJECT
  - DOMAIN-SUFFIX,umeng.com,REJECT
  - DOMAIN-SUFFIX,cnzz.com,REJECT
  - DOMAIN-SUFFIX,51.la,REJECT
  - DOMAIN-SUFFIX,growingio.com,REJECT

  # 使用规则集进行广告拦截（如果机场没有冲突的策略组名称）
  - RULE-SET,custom_ad_block_2025,REJECT
  - RULE-SET,custom_privacy_block,REJECT
  - RULE-SET,custom_hijacking_block,REJECT

# ⚠️ 使用说明：
# 1. 此配置文件只添加广告拦截规则，不会影响机场的代理分流
# 2. 所有广告域名都直接使用REJECT，不依赖机场的策略组
# 3. 如果需要自定义广告拦截策略组，请在机场配置中手动添加
# 4. 规则集使用独特命名避免冲突，如有重名请手动修改
