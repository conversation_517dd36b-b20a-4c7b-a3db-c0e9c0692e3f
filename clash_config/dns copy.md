rule-providers:
  reject:
    type: http
    behavior: domain
    url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt"
    path: ./ruleset/loyalsoldier/reject.yaml
    interval: 86400

prepend-rules:
  # 通用广告拦截
  - DOMAIN,securepubads.g.doubleclick.net,REJECT
  - DOMAIN,pubads.g.doubleclick.net,REJECT
  - DOMAIN,doubleclick.net,REJECT
  - DOMAIN,googlesyndication.com,REJECT
  - DOMAIN,admob.com,REJECT
  - DOMAIN,cm.pos.baidu.com,REJECT
  - DOMAIN,ad.qq.com,REJECT
  - DOMAIN,ads.twitter.com,REJECT
  - DOMAIN,ads-api.twitter.com,REJECT
  - DOMAIN,www.google.com,REJECT  # 若为广告流量，可拦截

  # Spotify 相关广告拦截（基于日志）
  - DOMAIN,aet.spotify.com,REJECT
  - <PERSON><PERSON><PERSON><PERSON>,api-partner.spotify.com,REJECT
  - <PERSON>OMAIN,ap-gae2.spotify.com,REJECT
  - DOMAIN,gae2-spclient.spotify.com,REJECT
  - DOMAIN,spclient.wg.spotify.com,REJECT
  - DOMAIN,gae2-dealer.g2.spotify.com,REJECT
  - DOMAIN,i.scdn.co,REJECT  # 若为广告 CDN，可拦截

  # 其他已知广告域名
  - DOMAIN,video-fa.scdn.co,REJECT
  - DOMAIN,rubiconproject.com,REJECT
  - DOMAIN,adtrafficquality.google,REJECT
  - DOMAIN,adclick.g.doubleclick.net,REJECT
  - DOMAIN,ads-fa.spotify.com,REJECT
  - DOMAIN,ads.spotify.com,REJECT
  - DOMAIN,analytics.spotify.com,REJECT
  - DOMAIN,cdn-spotify-experiments.conductrics.com,REJECT



  - RULE-SET, reject, REJECT
