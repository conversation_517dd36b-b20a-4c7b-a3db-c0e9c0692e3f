# 2025年DNS覆写配置 - 针对中国大陆网络环境优化
# 适用于mihomo内核 (Clash Meta) + Clash Verge Rev
# ⚠️ 此配置会覆盖机场的DNS设置，请确认机场没有特殊DNS要求

dns:
  enable: true
  listen: '0.0.0.0:53'
  enhanced-mode: 'fake-ip'
  fake-ip-range: '**********/16'
  fake-ip-filter-mode: 'blacklist'
  prefer-h3: true                    # 优先使用HTTP/3 DNS
  respect-rules: true                # 遵循分流规则
  use-hosts: true                    # 使用hosts文件
  use-system-hosts: false            # 不使用系统hosts
  ipv6: false                        # 禁用IPv6（中国大陆网络优化）

  # Fake-IP过滤列表 - 2025年更新
  fake-ip-filter:
    - '*.lan'
    - '*.local'
    - '*.arpa'
    - 'dns.msftncsi.com'
    - 'www.msftncsi.com'
    - '*.msftncsi.com'
    - 'www.msftconnecttest.com'
    - 'time.*.com'
    - 'ntp.*.com'
    - '*.srv.nintendo.net'
    - '*.stun.playstation.net'
    - 'xbox.*.microsoft.com'
    - '*.xboxlive.com'
    - 'localhost.ptlogin2.qq.com'
    - '*.teafone.com'
    - '*.sktswe.net'
    - 'rtc.goodfone.co.kr'
    - '*.chattti.com'
    - '*.market.xiaomi.com'
    - '*.apple.com'                  # 2025年新增
    - '*.icloud.com'                 # 2025年新增
    - '*.mzstatic.com'               # 2025年新增

  # 默认DNS服务器 - 用于解析其他DNS服务器和节点域名
  default-nameserver:
    - '*********'                    # 阿里DNS
    - '************'                 # 腾讯DNS
    - '**********'                   # 腾讯DNS备用

  # 主要DNS服务器 - 2025年优化组合
  nameserver:
    - 'https://*********/dns-query'  # 阿里DoH
    - 'https://*********/dns-query'  # 阿里DoH备用
    - 'https://**********/dns-query' # 腾讯DoH
    - 'tls://*******'                # Cloudflare DoT
    - 'tls://*******'                # Cloudflare DoT备用

  # 代理服务器DNS - 用于解析代理服务器域名
  proxy-server-nameserver:
    - 'https://*********/dns-query'
    - 'https://**********/dns-query'
    - 'tls://*********'
    - 'tls://**********'

  # 直连DNS服务器
  direct-nameserver: []
  direct-nameserver-follow-policy: true

  # 回退DNS服务器
  fallback:
    - 'tls://*******'                # Quad9 DoT
    - 'tls://***************'        # Quad9 DoT备用
    - 'https://doh.opendns.com/dns-query'  # OpenDNS DoH
    - 'https://doh.quad9.net/dns-query'    # Quad9 DoH

  # 回退过滤器 - 2025年优化
  fallback-filter:
    geoip: true
    geoip-code: 'CN'
    ipcidr:
      - '240.0.0.0/4'
      - '0.0.0.0/32'
      - '127.0.0.1/32'
    domain:
      - '+.google.com'
      - '+.facebook.com'
      - '+.youtube.com'
      - '+.twitter.com'
      - '+.instagram.com'
      - '+.telegram.org'
      - '+.github.com'               # 2025年新增
      - '+.openai.com'               # 2025年新增
      - '+.anthropic.com'            # 2025年新增

  # DNS分流策略 - 2025年完整版
  nameserver-policy:
    # 私有网络使用系统DNS
    'geosite:private': 'system'

    # 中国大陆域名使用国内DNS
    'geosite:cn':
      - '*********'
      - '************'
      - '**********'

    # 国外域名使用加密DNS
    'geosite:geolocation-!cn':
      - 'https://*******/dns-query'
      - 'https://*******/dns-query'
      - 'tls://*******'

    # 特定服务优化 - 2025年新增
    '+.google.com':
      - 'https://*******/dns-query'
      - 'tls://*******'

    '+.cloudflare.com':
      - 'https://*******/dns-query'
      - 'tls://*******'

    '+.apple.com':
      - '*********'
      - '************'

    '+.microsoft.com':
      - '*********'
      - 'https://*******/dns-query'

    '+.github.com':
      - 'https://*******/dns-query'
      - 'tls://*******'

    '+.openai.com':
      - 'https://*******/dns-query'
      - 'tls://*******'

    '+.anthropic.com':
      - 'https://*******/dns-query'
      - 'tls://*******'

# ⚠️ 使用说明：
# 1. 此DNS配置针对中国大陆网络环境优化
# 2. 使用国内外DNS分流，提高解析速度和准确性
# 3. 启用DoH/DoT加密DNS，提高隐私保护
# 4. 如果机场有特殊DNS要求，请谨慎使用此配置
# 5. 建议在测试环境中验证后再正式使用
