dns:
  enable: true
  listen: '0.0.0.0:53'
  enhanced-mode: 'fake-ip'
  fake-ip-range: '**********/16'
  fake-ip-filter-mode: 'blacklist'
  prefer-h3: true
  respect-rules: true
  use-hosts: false
  use-system-hosts: false
  ipv6: false
  fake-ip-filter:
    - '*.lan'
    - '*.local'
    - '*.arpa'
    - 'dns.msftncsi.com'
    - 'www.msftncsi.com'
    - '*.msftncsi.com'
    - 'www.msftconnecttest.com'
    - 'time.*.com'
    - 'ntp.*.com'
    - '*.srv.nintendo.net'
    - '*.stun.playstation.net'
    - 'xbox.*.microsoft.com'
    - '*.xboxlive.com'
    - 'localhost.ptlogin2.qq.com'
    - '*.teafone.com'
    - '*.sktswe.net'
    - 'rtc.goodfone.co.kr'
    - '*.chattti.com'
    - '*.market.xiaomi.com'
    - '*.apple.com'
    - '*.icloud.com'
    - '*.mzstatic.com'
    - '*.steamcontent.com'
  default-nameserver:
    - '*********'
    - '************'
    - '***************'
  nameserver:
    - 'https://dns.adguard-dns.com/dns-query'
    - 'https://public.dns.iij.jp/dns-query'
    - 'tls://*******'
    - 'tls://*******'
  direct-nameserver-follow-policy: true
  fallback-filter:
    geoip: true
    geoip-code: 'CN'
    ipcidr:
      - '240.0.0.0/4'
      - '0.0.0.0/32'
    domain:
      - '+.google.com'
      - '+.facebook.com'
      - '+.youtube.com'
      - '+.twitter.com'
      - '+.instagram.com'
      - '+.telegram.org'
  fallback:
    - 'tls://*******'
    - 'https://doh.opendns.com/dns-query'
  proxy-server-nameserver:
    - 'https://dns.alidns.com/dns-query'
    - 'tls://*********'
    - 'tls://************'
    - 'tls://**********'
  direct-nameserver: []
  nameserver-policy:
    geosite:cn:
      - '*********'
      - '************'
      - '***************'
