/**
 * Clash Verge Rev 2025年全局扩展脚本
 * 适用于mihomo内核 (Clash Meta)
 * 针对中国大陆网络环境优化
 *
 * 功能特性：
 * - 智能DNS配置
 * - 动态广告拦截
 * - 自动代理组生成
 * - 性能优化设置
 * - 2025年最新规则集
 */

function main(config) {
  // 工具函数
  const isObject = (value) => {
    return value !== null && typeof value === 'object'
  }

  const mergeConfig = (existingConfig, newConfig) => {
    if (!isObject(existingConfig)) {
      existingConfig = {}
    }
    if (!isObject(newConfig)) {
      return existingConfig
    }
    return { ...existingConfig, ...newConfig }
  }

  // 2025年广告域名黑名单 - 持续更新
  const adDomains = [
    // Google广告生态系统
    "securepubads.g.doubleclick.net",
    "pubads.g.doubleclick.net",
    "doubleclick.net",
    "googlesyndication.com",
    "googleadservices.com",
    "googletagmanager.com",
    "google-analytics.com",
    "admob.com",
    "adsystem.google.com",
    "adsystem.google",
    "adtrafficquality.google",
    "adclick.g.doubleclick.net",

    // 百度广告系统
    "cm.pos.baidu.com",
    "cpro.baidu.com",
    "pos.baidu.com",
    "eiv.baidu.com",
    "nsclick.baidu.com",

    // 腾讯广告系统
    "ad.qq.com",
    "ads.qq.com",
    "btrace.qq.com",
    "pingjs.qq.com",
    "pingma.qq.com",

    // 社交媒体广告
    "ads.twitter.com",
    "ads-api.twitter.com",
    "analytics.twitter.com",
    "ads.facebook.com",
    "an.facebook.com",
    "connect.facebook.net",

    // Spotify广告系统 - 2025年更新
    "aet.spotify.com",
    "api-partner.spotify.com",
    "ap-gae2.spotify.com",
    "gae2-spclient.spotify.com",
    "spclient.wg.spotify.com",
    "gae2-dealer.g2.spotify.com",
    "ads-fa.spotify.com",
    "ads.spotify.com",
    "analytics.spotify.com",
    "cdn-spotify-experiments.conductrics.com",
    "video-fa.scdn.co",

    // 第三方广告网络 - 2025年新增
    "rubiconproject.com",
    "adnxs.com",
    "amazon-adsystem.com",
    "criteo.com",
    "outbrain.com",
    "taboola.com",
    "adsystem.amazon.com",
    "amazon-adsystem.com",
    "googletag.com",
    "googletagservices.com",

    // 移动广告平台
    "unity3d.com",
    "unityads.unity3d.com",
    "applifier.com",
    "chartboost.com",
    "vungle.com",
    "applovin.com",
    "ironsrc.com",

    // 国内广告平台
    "tanx.com",
    "alimama.com",
    "mmstat.com",
    "umeng.com",
    "cnzz.com",
    "51.la",
    "growingio.com"
  ]

  // DNS配置 - 2025年优化版本
  const cnDnsList = [
    'https://*********/dns-query',
    'https://*********/dns-query',
    'https://**********/dns-query'
  ]

  const trustDnsList = [
    'https://*******/dns-query',
    'https://*******/dns-query',
    'https://*******/dns-query',
    'tls://dns.google'
  ]

  const dnsOptions = {
    'enable': true,
    'listen': ':53',
    'ipv6': false,
    'prefer-h3': true,
    'use-hosts': true,
    'use-system-hosts': false,
    'respect-rules': true,
    'enhanced-mode': 'fake-ip',
    'fake-ip-range': '**********/16',
    'fake-ip-filter': [
      '*.lan',
      '*.local',
      '*.arpa',
      'dns.msftncsi.com',
      'www.msftncsi.com',
      '*.msftncsi.com',
      'www.msftconnecttest.com',
      'time.*.com',
      'ntp.*.com',
      '*.srv.nintendo.net',
      '*.stun.playstation.net',
      'xbox.*.microsoft.com',
      '*.xboxlive.com',
      'localhost.ptlogin2.qq.com',
      '*.market.xiaomi.com'
    ],
    'default-nameserver': cnDnsList,
    'nameserver': trustDnsList,
    'proxy-server-nameserver': trustDnsList,
    'nameserver-policy': {
      'geosite:private': 'system',
      'geosite:cn': cnDnsList,
      'geosite:geolocation-!cn': trustDnsList,
      '+.google.com': trustDnsList,
      '+.facebook.com': trustDnsList,
      '+.youtube.com': trustDnsList,
      '+.twitter.com': trustDnsList,
      '+.instagram.com': trustDnsList,
      '+.telegram.org': trustDnsList
    },
    'fallback': [
      'tls://*******',
      'https://doh.opendns.com/dns-query'
    ],
    'fallback-filter': {
      'geoip': true,
      'geoip-code': 'CN',
      'ipcidr': [
        '240.0.0.0/4',
        '0.0.0.0/32'
      ],
      'domain': [
        '+.google.com',
        '+.facebook.com',
        '+.youtube.com',
        '+.twitter.com',
        '+.instagram.com',
        '+.telegram.org'
      ]
    }
  }

  // GitHub加速前缀
  const githubPrefix = 'https://fastly.jsdelivr.net/'

  // GEO数据GitHub资源原始下载地址
  const rawGeoxURLs = {
    geoip: 'https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat',
    geosite: 'https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat',
    mmdb: 'https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/country-lite.mmdb',
  }

  // 生成带有加速前缀的GEO数据资源对象
  const accelURLs = Object.fromEntries(
    Object.entries(rawGeoxURLs).map(([key, githubUrl]) => [key, `${githubPrefix}${githubUrl}`]),
  )

  // 其他优化配置
  const otherOptions = {
    'allow-lan': true,
    'bind-address': '*',
    'mode': 'rule',
    'log-level': 'info',
    'unified-delay': true,
    'tcp-concurrent': true,
    'keep-alive-interval': 1800,
    'find-process-mode': 'strict',
    'external-controller': '127.0.0.1:9090',
    'external-controller-tls': '127.0.0.1:9443',
    'geodata-mode': true,
    'geodata-loader': 'memconservative',
    'geo-auto-update': true,
    'geo-update-interval': 24,
    'geox-url': accelURLs,
    'profile': {
      'store-selected': true,
      'store-fake-ip': true,
    },
    'sniffer': {
      enable: true,
      'force-dns-mapping': true,
      'parse-pure-ip': true,
      'override-destination': false,
      sniff: {
        TLS: {
          ports: [443, 8443],
        },
        HTTP: {
          'ports': [80, '8080-8880'],
        },
        QUIC: {
          ports: [443, 8443],
        },
      },
      'force-domain': [],
      'skip-domain': [
        'Mijia Cloud',
        '+.oray.com'
      ],
    },
    'ntp': {
      enable: true,
      'write-to-system': false,
      server: 'cn.ntp.org.cn',
    }
  }

  // 应用DNS配置
  config.dns = mergeConfig(config.dns, dnsOptions)

  // 应用其他配置
  config = { ...config, ...otherOptions }

  // 动态广告拦截逻辑
  if (config.rules) {
    const adRules = adDomains.map(domain => `DOMAIN-SUFFIX,${domain},REJECT`)
    config.rules = [...adRules, ...config.rules]
  }

  // 如果没有代理节点，直接返回
  if (!config?.proxies || config.proxies.length === 0) {
    return config
  }

  // 代理组通用配置
  const groupBaseOption = {
    interval: 300,
    timeout: 3000,
    url: 'https://www.google.com/generate_204',
    lazy: true,
    'max-failed-times': 3,
    hidden: false,
  }

  // 智能代理组生成
  const autoProxyGroups = [
    {
      ...groupBaseOption,
      name: '🚀 节点选择',
      type: 'select',
      proxies: [
        '♻️ 自动选择',
        '🔯 故障转移',
        '🔮 负载均衡',
        '🎯 全球直连'
      ],
      'include-all': true,
    },
    {
      ...groupBaseOption,
      name: '♻️ 自动选择',
      type: 'url-test',
      tolerance: 50,
      'include-all': true,
    },
    {
      ...groupBaseOption,
      name: '🔯 故障转移',
      type: 'fallback',
      'include-all': true,
    },
    {
      ...groupBaseOption,
      name: '🔮 负载均衡',
      type: 'load-balance',
      strategy: 'consistent-hashing',
      'include-all': true,
    },
    {
      ...groupBaseOption,
      name: '🎯 全球直连',
      type: 'select',
      proxies: ['DIRECT', '🚀 节点选择'],
    },
    {
      ...groupBaseOption,
      name: '🛑 广告拦截',
      type: 'select',
      proxies: ['REJECT', 'DIRECT'],
    },
    {
      ...groupBaseOption,
      name: '🐟 漏网之鱼',
      type: 'select',
      proxies: ['🚀 节点选择', '🎯 全球直连'],
      'include-all': true,
    }
  ]

  // 如果原配置没有代理组，则使用自动生成的
  if (!config['proxy-groups'] || config['proxy-groups'].length === 0) {
    config['proxy-groups'] = autoProxyGroups
  }

  return config
}
