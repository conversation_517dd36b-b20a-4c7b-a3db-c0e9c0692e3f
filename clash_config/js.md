/**
 * Clash Verge Rev 2025年全局扩展脚本 - 无冲突版本
 * 适用于mihomo内核 (Clash Meta)
 * 针对中国大陆网络环境优化
 *
 * ⚠️ 重要说明：此脚本采用"附加"模式，避免与机场配置冲突
 *
 * 功能特性：
 * - 动态广告拦截规则插入（不覆盖机场规则）
 * - 性能优化设置（不影响机场核心配置）
 * - 智能策略组补充（仅在缺失时添加）
 * - GEO数据源优化
 *
 * ⚠️ 已移除的功能（避免冲突）：
 * - DNS配置（请使用dns.md文件）
 * - 完整代理组覆盖
 * - 规则集定义
 */

function main(config) {
  // 工具函数
  const isObject = (value) => {
    return value !== null && typeof value === 'object'
  }

  // 2025年广告域名黑名单 - 动态插入到规则最前面
  const adDomains = [
    // Google广告生态系统
    "securepubads.g.doubleclick.net",
    "pubads.g.doubleclick.net",
    "doubleclick.net",
    "googlesyndication.com",
    "googleadservices.com",
    "googletagmanager.com",
    "google-analytics.com",
    "admob.com",
    "adsystem.google.com",
    "adsystem.google",
    "adtrafficquality.google",
    "adclick.g.doubleclick.net",
    "googletag.com",
    "googletagservices.com",

    // 百度广告系统
    "cm.pos.baidu.com",
    "cpro.baidu.com",
    "pos.baidu.com",
    "eiv.baidu.com",
    "nsclick.baidu.com",

    // 腾讯广告系统
    "ad.qq.com",
    "ads.qq.com",
    "btrace.qq.com",
    "pingjs.qq.com",
    "pingma.qq.com",

    // 社交媒体广告
    "ads.twitter.com",
    "ads-api.twitter.com",
    "analytics.twitter.com",
    "ads.facebook.com",
    "an.facebook.com",
    "connect.facebook.net",

    // Spotify广告系统 - 2025年更新
    "aet.spotify.com",
    "api-partner.spotify.com",
    "ap-gae2.spotify.com",
    "gae2-spclient.spotify.com",
    "spclient.wg.spotify.com",
    "gae2-dealer.g2.spotify.com",
    "ads-fa.spotify.com",
    "ads.spotify.com",
    "analytics.spotify.com",
    "cdn-spotify-experiments.conductrics.com",
    "video-fa.scdn.co",

    // 第三方广告网络 - 2025年新增
    "rubiconproject.com",
    "adnxs.com",
    "amazon-adsystem.com",
    "criteo.com",
    "outbrain.com",
    "taboola.com",
    "adsystem.amazon.com",

    // 移动广告平台
    "unity3d.com",
    "unityads.unity3d.com",
    "applifier.com",
    "chartboost.com",
    "vungle.com",
    "applovin.com",
    "ironsrc.com",

    // 国内广告平台
    "tanx.com",
    "alimama.com",
    "mmstat.com",
    "umeng.com",
    "cnzz.com",
    "51.la",
    "growingio.com"
  ]

  // 生成带有加速前缀的GEO数据资源对象
  const accelURLs = Object.fromEntries(
    Object.entries(rawGeoxURLs).map(([key, githubUrl]) => [key, `${githubPrefix}${githubUrl}`]),
  )

  // ⚠️ 性能优化配置 - 这些设置通常不会与机场配置冲突
  // 但如果机场有特殊要求，请注释掉相应行
  const performanceOptions = {
    'unified-delay': true,           // 统一延迟测试
    'tcp-concurrent': true,          // TCP并发连接
    'keep-alive-interval': 1800,     // 保活间隔（秒）
    'find-process-mode': 'strict',   // 进程查找模式
    'geodata-mode': true,            // 使用GEO数据模式
    'geodata-loader': 'memconservative', // 内存保守模式
    'geo-auto-update': true,         // 自动更新GEO数据
    'geo-update-interval': 24,       // GEO数据更新间隔（小时）
    'geox-url': accelURLs,          // 使用加速的GEO数据源
  }

  // ⚠️ 域名嗅探配置 - 可能与机场配置冲突
  // 如果机场已配置sniffer，此处会覆盖，请根据需要调整
  const snifferOptions = {
    'sniffer': {
      enable: true,
      'force-dns-mapping': true,
      'parse-pure-ip': true,
      'override-destination': false,  // 重要：设为false避免某些应用问题
      sniff: {
        TLS: {
          ports: [443, 8443],
        },
        HTTP: {
          'ports': [80, '8080-8880'],
        },
        QUIC: {
          ports: [443, 8443],
        },
      },
      'skip-domain': [
        'Mijia Cloud',
        '+.oray.com'
      ],
    }
  }

  // ⚠️ NTP时间同步 - 通常不冲突，但可选
  const ntpOptions = {
    'ntp': {
      enable: true,
      'write-to-system': false,  // 不写入系统时间，避免权限问题
      server: 'cn.ntp.org.cn',
    }
  }

  // 应用性能优化配置（通常安全）
  Object.assign(config, performanceOptions)

  // ⚠️ 应用域名嗅探配置（可能冲突，请根据需要启用）
  // Object.assign(config, snifferOptions)

  // 应用NTP配置（可选）
  Object.assign(config, ntpOptions)

  // ⚠️ 动态广告拦截逻辑 - 将广告规则插入到最前面
  // 这是核心功能，确保广告拦截优先级最高
  if (config.rules && Array.isArray(config.rules)) {
    const adRules = adDomains.map(domain => `DOMAIN-SUFFIX,${domain},REJECT`)
    // 将广告拦截规则插入到现有规则的最前面
    config.rules = [...adRules, ...config.rules]
  } else if (!config.rules) {
    // 如果机场没有规则，创建基础广告拦截规则
    const adRules = adDomains.map(domain => `DOMAIN-SUFFIX,${domain},REJECT`)
    config.rules = adRules
  }

  // ⚠️ 策略组补充逻辑 - 仅在机场没有提供时添加基础策略组
  // 这部分代码被注释掉，避免与机场配置冲突
  // 如果您的机场没有提供策略组，可以取消注释下方代码

  /*
  // 检查是否缺少基础策略组
  if (!config['proxy-groups'] || config['proxy-groups'].length === 0) {
    console.log('检测到机场未提供策略组，添加基础策略组')

    const groupBaseOption = {
      interval: 300,
      timeout: 3000,
      url: 'https://www.google.com/generate_204',
      lazy: true,
      'max-failed-times': 3,
      hidden: false,
    }

    // 基础策略组配置
    const basicProxyGroups = [
      {
        ...groupBaseOption,
        name: '🚀 节点选择',
        type: 'select',
        proxies: ['♻️ 自动选择', '🔯 故障转移', 'DIRECT'],
        'include-all': true,
      },
      {
        ...groupBaseOption,
        name: '♻️ 自动选择',
        type: 'url-test',
        tolerance: 50,
        'include-all': true,
      },
      {
        ...groupBaseOption,
        name: '🔯 故障转移',
        type: 'fallback',
        'include-all': true,
      }
    ]

    config['proxy-groups'] = basicProxyGroups
  }
  */

  // ⚠️ 规则集补充 - 避免与机场冲突，已注释
  // 如果需要添加规则集，请使用config.md文件

  /*
  if (!config['rule-providers']) {
    config['rule-providers'] = {}
  }

  // 添加广告拦截规则集（仅在不存在时）
  if (!config['rule-providers']['ad-block']) {
    config['rule-providers']['ad-block'] = {
      type: 'http',
      behavior: 'domain',
      url: 'https://fastly.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Advertising/Advertising.yaml',
      path: './ruleset/advertising.yaml',
      interval: 86400
    }
  }
  */

  return config
}

// ⚠️ 使用说明：
// 1. 此脚本主要功能是动态插入广告拦截规则到机场规则最前面
// 2. 性能优化设置通常不会与机场冲突
// 3. DNS配置已移除，请使用dns.md文件
// 4. 策略组生成已注释，避免覆盖机场配置
// 5. 如需自定义策略组，请取消注释相应代码段
