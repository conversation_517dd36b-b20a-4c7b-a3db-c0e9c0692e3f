function main(config) {

​    const adDomains = [

​        "securepubads.g.doubleclick.net",

​        "pubads.g.doubleclick.net",

​        "doubleclick.net",

​        "googlesyndication.com",

​        "admob.com",

​        "cm.pos.baidu.com",

​        "ad.qq.com",

​        "ads.twitter.com",

​        "ads-api.twitter.com",

​        "aet.spotify.com",

​        "api-partner.spotify.com",

​        "ap-gae2.spotify.com",

​        "gae2-spclient.spotify.com",

​        "spclient.wg.spotify.com",

​        "gae2-dealer.g2.spotify.com",

​        "i.scdn.co", // 若为广告 CDN

​        "video-fa.scdn.co",

​        "rubiconproject.com",

​        "adtrafficquality.google",

​        "adclick.g.doubleclick.net",

​        "ads-fa.spotify.com",

​        "ads.spotify.com",

​        "analytics.spotify.com",

​        "cdn-spotify-experiments.conductrics.com"

​    ];



​    // 检查请求域名是否在广告域名列表中

​    for (const domain of adDomains) {

​        if (config.host.includes(domain)) {

​            return { policy: "REJECT" }; // 强制拒绝

​        }

​    }



​    // 未匹配广告域名，返回原始配置

​    return config;

}
