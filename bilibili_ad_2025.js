// ==UserScript==
// @name         Bilibili Ad Blocker 2025 (Working Edition)
// @version      1.0
// @description  Simple but effective Bilibili ad blocker that preserves UI
// <AUTHOR> Assistant
// @match        *://bilibili.com/*
// @match        *://*.bilibili.com/*
// @run-at       document-start
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    
    // ===== BILIBILI-SPECIFIC CONFIGURATION =====
    const CONFIG = {
        DEBUG: false,
        
        // Only target actual ads, not UI elements
        AD_SELECTORS: [
            // Explicit ad containers
            '.ad-report',
            '.advertisement',
            '.advertisement-banner',
            '.commercial-banner',
            
            // Data-driven ads
            '[data-report*="ad"]:not([class*="video"]):not([class*="card"])',
            '[data-ad-creative-type]',
            '[data-spm*="ad"]:not([class*="video"])',
            
            // Eva advertising system
            '.eva-banner[data-loc*="ad"]',
            '.eva-extension',
            
            // Feed ads (be very specific)
            '.feed-ad-container',
            '.recommend-ad-card',
            '.live-ad-card',
            '.pgc-ad-card',
            
            // Popup ads
            '.popup-ad',
            '.modal-ad',
            '.dialog-ad'
        ],
        
        // Elements to explicitly protect
        PROTECTED_SELECTORS: [
            '.banner:not([class*="ad"])',
            '.bili-banner:not([class*="ad"])',
            '.video-banner',
            '.carousel',
            '.video-card',
            '.bili-video-card',
            '.feed-card',
            '.recommend-item',
            '.small-item',
            '.header',
            '.bili-header',
            '.nav'
        ]
    };

    // ===== LOGGING =====
    const log = (...args) => CONFIG.DEBUG && console.log('[Bilibili-AdBlock-2025]', ...args);

    // ===== SAFE CSS INJECTION =====
    function injectSafeCSS() {
        if (document.getElementById('bilibili-adblock-2025')) return;
        
        const style = document.createElement('style');
        style.id = 'bilibili-adblock-2025';
        
        // Only hide explicit ad selectors
        const adCSS = CONFIG.AD_SELECTORS.map(selector => 
            `${selector} { display: none !important; visibility: hidden !important; opacity: 0 !important; }`
        ).join('\n');
        
        // Ensure protected elements remain visible
        const protectedCSS = CONFIG.PROTECTED_SELECTORS.map(selector => 
            `${selector} { display: block !important; visibility: visible !important; opacity: 1 !important; }`
        ).join('\n');
        
        style.textContent = `
            /* Hide ads */
            ${adCSS}
            
            /* Protect UI elements */
            ${protectedCSS}
            
            /* Fix potential layout issues */
            .bili-feed4 .feed-card,
            .recommended-container .video-card,
            .rec-list .small-item {
                display: block !important;
                visibility: visible !important;
            }
        `;
        
        (document.head || document.documentElement).appendChild(style);
        log('Safe CSS injected');
    }

    // ===== SMART ELEMENT PROCESSING =====
    function processElements() {
        let adsBlocked = 0;
        let elementsProtected = 0;
        
        // Block ads
        CONFIG.AD_SELECTORS.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    // Double-check this isn't a protected element
                    if (!isProtectedElement(element)) {
                        if (element.style.display !== 'none') {
                            hideElement(element);
                            adsBlocked++;
                        }
                    }
                });
            } catch (err) {
                // Ignore selector errors
            }
        });
        
        // Protect UI elements
        CONFIG.PROTECTED_SELECTORS.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element.style.display === 'none') {
                        showElement(element);
                        elementsProtected++;
                    }
                });
            } catch (err) {
                // Ignore selector errors
            }
        });
        
        if (adsBlocked > 0 || elementsProtected > 0) {
            log(`Blocked ${adsBlocked} ads, protected ${elementsProtected} UI elements`);
        }
    }

    function isProtectedElement(element) {
        // Check if element matches any protected selector
        return CONFIG.PROTECTED_SELECTORS.some(selector => {
            try {
                return element.matches(selector);
            } catch (err) {
                return false;
            }
        });
    }

    function hideElement(element) {
        element.style.setProperty('display', 'none', 'important');
        element.style.setProperty('visibility', 'hidden', 'important');
        element.style.setProperty('opacity', '0', 'important');
    }

    function showElement(element) {
        element.style.removeProperty('display');
        element.style.removeProperty('visibility');
        element.style.removeProperty('opacity');
    }

    // ===== CONSERVATIVE OBSERVER =====
    function setupObserver() {
        const observer = new MutationObserver((mutations) => {
            let shouldProcess = false;
            
            // Only process if we detect potential ad-related changes
            for (const mutation of mutations) {
                if (mutation.addedNodes.length > 0) {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const className = node.className || '';
                            if (className.includes('ad') || 
                                className.includes('eva') || 
                                className.includes('commercial')) {
                                shouldProcess = true;
                                break;
                            }
                        }
                    }
                }
                if (shouldProcess) break;
            }
            
            if (shouldProcess) {
                // Throttle processing
                setTimeout(processElements, 200);
            }
        });
        
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true
        });
        
        log('Conservative observer setup');
    }

    // ===== BILIBILI-SPECIFIC HANDLERS =====
    function handleBilibiliSpecific() {
        // Handle dynamic content loading
        const handleDynamicContent = () => {
            // Check for new feed content
            const feedContainers = [
                '.recommended-container',
                '.bili-feed4',
                '.feed4-layout'
            ];
            
            feedContainers.forEach(containerSelector => {
                const container = document.querySelector(containerSelector);
                if (container) {
                    const observer = new MutationObserver(() => {
                        setTimeout(processElements, 300);
                    });
                    
                    observer.observe(container, {
                        childList: true,
                        subtree: true
                    });
                }
            });
        };
        
        // Wait for page to load before setting up dynamic handlers
        setTimeout(handleDynamicContent, 2000);
        
        log('Bilibili-specific handlers setup');
    }

    // ===== INITIALIZATION =====
    function init() {
        log('Initializing Bilibili Ad Blocker 2025...');
        
        // Inject safe CSS
        injectSafeCSS();
        
        // Initial processing
        processElements();
        
        // Setup observer
        setupObserver();
        
        // Setup Bilibili-specific handlers
        handleBilibiliSpecific();
        
        // Periodic maintenance (less frequent to avoid performance issues)
        setInterval(processElements, 10000);
        
        log('Bilibili Ad Blocker 2025 initialized');
    }

    // ===== START =====
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Backup initialization
    setTimeout(init, 1000);
    
    // Handle page navigation
    let currentUrl = location.href;
    setInterval(() => {
        if (location.href !== currentUrl) {
            currentUrl = location.href;
            log('Page navigation detected, reprocessing...');
            setTimeout(processElements, 500);
        }
    }, 1000);

})();
