// ==UserScript==
// @name         Universal Ad Blocker 2025 (Simplified Working Edition)
// @version      1.0
// @description  Simple but effective universal ad blocker based on 2025 best practices
// <AUTHOR> Assistant
// @match        *://*/*
// @exclude      *://www.youtube.com/*
// @exclude      *://*.youtube.com/*
// @exclude      *://bilibili.com/*
// @exclude      *://*.bilibili.com/*
// @run-at       document-start
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    
    // ===== SIMPLE BUT EFFECTIVE CONFIGURATION =====
    const CONFIG = {
        DEBUG: false,
        
        // Simplified selectors that actually work
        AD_SELECTORS: [
            // Google Ads
            '.adsbygoogle',
            '[data-ad-slot]',
            '[data-ad-client]',
            
            // Generic ads
            '.advertisement',
            '.ad-container',
            '.ad-banner',
            '.ad-wrapper',
            '.sponsored',
            '.promotion',
            
            // Social media ads
            '[data-testid*="ad"]',
            '[aria-label*="Sponsored"]',
            '[aria-label*="Advertisement"]',
            
            // Common ad networks
            '[class*="outbrain"]',
            '[class*="taboola"]',
            '[class*="criteo"]',
            
            // Popup and overlay ads
            '.popup-ad',
            '.overlay-ad',
            '.modal-ad'
        ],
        
        // Network blocking patterns
        BLOCKED_DOMAINS: [
            'doubleclick.net',
            'googlesyndication.com',
            'googletagmanager.com',
            'googletagservices.com',
            'amazon-adsystem.com',
            'facebook.com/tr',
            'outbrain.com',
            'taboola.com',
            'criteo.com'
        ]
    };

    // ===== LOGGING =====
    const log = (...args) => CONFIG.DEBUG && console.log('[Universal-AdBlock-2025]', ...args);

    // ===== SIMPLE CSS INJECTION =====
    function injectCSS() {
        if (document.getElementById('universal-adblock-2025')) return;
        
        const style = document.createElement('style');
        style.id = 'universal-adblock-2025';
        
        // Generate CSS from selectors
        const css = CONFIG.AD_SELECTORS.map(selector => 
            `${selector} { display: none !important; visibility: hidden !important; opacity: 0 !important; }`
        ).join('\n');
        
        style.textContent = css;
        (document.head || document.documentElement).appendChild(style);
        log('CSS injected');
    }

    // ===== NETWORK BLOCKING =====
    function setupNetworkBlocking() {
        // Override fetch
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && shouldBlockUrl(url)) {
                log('Blocked fetch:', url);
                return Promise.reject(new Error('Blocked by ad blocker'));
            }
            return originalFetch.apply(this, args);
        };
        
        // Override XMLHttpRequest
        const originalXHR = window.XMLHttpRequest;
        window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalOpen = xhr.open;
            
            xhr.open = function(method, url, ...args) {
                if (shouldBlockUrl(url)) {
                    log('Blocked XHR:', url);
                    return;
                }
                return originalOpen.apply(this, [method, url, ...args]);
            };
            
            return xhr;
        };
        
        log('Network blocking setup');
    }

    function shouldBlockUrl(url) {
        if (!url || typeof url !== 'string') return false;
        
        return CONFIG.BLOCKED_DOMAINS.some(domain => url.includes(domain));
    }

    // ===== DOM ELEMENT BLOCKING =====
    function processElements() {
        let blocked = 0;
        
        CONFIG.AD_SELECTORS.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element.style.display !== 'none') {
                        element.style.setProperty('display', 'none', 'important');
                        element.style.setProperty('visibility', 'hidden', 'important');
                        element.style.setProperty('opacity', '0', 'important');
                        blocked++;
                    }
                });
            } catch (err) {
                // Ignore selector errors
            }
        });
        
        if (blocked > 0) {
            log(`Blocked ${blocked} ad elements`);
        }
    }

    // ===== MUTATION OBSERVER =====
    function setupObserver() {
        const observer = new MutationObserver((mutations) => {
            let shouldProcess = false;
            
            for (const mutation of mutations) {
                if (mutation.addedNodes.length > 0) {
                    shouldProcess = true;
                    break;
                }
            }
            
            if (shouldProcess) {
                // Throttle processing
                setTimeout(processElements, 100);
            }
        });
        
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true
        });
        
        log('Observer setup');
    }

    // ===== SITE-SPECIFIC HANDLERS =====
    function handleSiteSpecific() {
        const hostname = window.location.hostname.toLowerCase();
        
        // Facebook
        if (hostname.includes('facebook.com')) {
            const fbSelectors = [
                '[data-pagelet="RightRail"]',
                '[aria-label*="Sponsored"]'
            ];
            CONFIG.AD_SELECTORS.push(...fbSelectors);
        }
        
        // Twitter/X
        if (hostname.includes('twitter.com') || hostname.includes('x.com')) {
            const twitterSelectors = [
                '[data-testid="placementTracking"]',
                '[data-testid="trend"] [aria-label*="Promoted"]'
            ];
            CONFIG.AD_SELECTORS.push(...twitterSelectors);
        }
        
        // Reddit
        if (hostname.includes('reddit.com')) {
            const redditSelectors = [
                '.promotedlink',
                '[data-promoted="true"]'
            ];
            CONFIG.AD_SELECTORS.push(...redditSelectors);
        }
        
        log('Site-specific selectors added for:', hostname);
    }

    // ===== INITIALIZATION =====
    function init() {
        log('Initializing Universal Ad Blocker 2025...');
        
        // Handle site-specific cases
        handleSiteSpecific();
        
        // Inject CSS immediately
        injectCSS();
        
        // Setup network blocking
        setupNetworkBlocking();
        
        // Initial element processing
        processElements();
        
        // Setup observer for dynamic content
        setupObserver();
        
        // Periodic cleanup
        setInterval(processElements, 5000);
        
        log('Universal Ad Blocker 2025 initialized');
    }

    // ===== START =====
    // Initialize immediately if possible
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Backup initialization
    setTimeout(init, 500);

})();
