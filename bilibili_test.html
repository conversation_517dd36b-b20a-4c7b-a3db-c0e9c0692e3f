<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B站广告屏蔽测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f5f7;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e1e2e3;
            border-radius: 8px;
            background: #fafbfc;
        }
        .test-section h3 {
            margin-top: 0;
            color: #18191a;
            border-bottom: 2px solid #00a1d6;
            padding-bottom: 8px;
        }
        
        /* 模拟B站正常元素 */
        .banner, .bili-banner, .video-banner {
            background: linear-gradient(45deg, #00a1d6, #fb7299);
            color: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        
        .video-card, .bili-video-card, .feed-card {
            background: white;
            border: 1px solid #e1e2e3;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header, .bili-header, .nav {
            background: #fff;
            border-bottom: 1px solid #e1e2e3;
            padding: 15px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        /* 模拟B站广告元素 */
        .should-be-hidden {
            background-color: #ffebee;
            color: #c62828;
            padding: 15px;
            margin: 10px 0;
            border: 2px solid #f44336;
            border-radius: 8px;
            position: relative;
        }
        
        .should-be-hidden::before {
            content: "🚫 广告 - 应该被屏蔽";
            position: absolute;
            top: -10px;
            left: 10px;
            background: #f44336;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .should-be-visible {
            background-color: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            margin: 10px 0;
            border: 2px solid #4caf50;
            border-radius: 8px;
            position: relative;
        }
        
        .should-be-visible::before {
            content: "✅ 正常内容 - 应该可见";
            position: absolute;
            top: -10px;
            left: 10px;
            background: #4caf50;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        
        button {
            background: #00a1d6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #0082a6;
        }
        
        #results {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ B站广告屏蔽测试页面</h1>
        <p>这个页面模拟B站的DOM结构，用于测试003.js脚本对B站的广告屏蔽效果和UI兼容性。</p>
        
        <div class="controls">
            <h3>测试控制</h3>
            <button onclick="checkStatus()">检查屏蔽状态</button>
            <button onclick="addDynamicContent()">添加动态内容</button>
            <button onclick="simulatePageLoad()">模拟页面加载</button>
            <button onclick="showStats()">显示统计</button>
        </div>
        
        <div id="results"></div>
        
        <!-- B站正常元素测试 -->
        <div class="test-section">
            <h3>B站正常元素（应该保持可见）</h3>
            
            <div class="header should-be-visible">
                B站页面头部 (.header)
            </div>
            
            <div class="bili-header should-be-visible">
                B站专用头部 (.bili-header)
            </div>
            
            <div class="nav should-be-visible">
                导航栏 (.nav)
            </div>
            
            <div class="banner should-be-visible">
                轮播横幅 (.banner)
            </div>
            
            <div class="bili-banner should-be-visible">
                B站横幅 (.bili-banner)
            </div>
            
            <div class="video-banner should-be-visible">
                视频横幅 (.video-banner)
            </div>
            
            <div class="grid">
                <div class="video-card should-be-visible">
                    <h4>视频卡片 (.video-card)</h4>
                    <p>这是一个正常的视频卡片，应该保持可见</p>
                </div>
                
                <div class="bili-video-card should-be-visible">
                    <h4>B站视频卡片 (.bili-video-card)</h4>
                    <p>这是B站专用的视频卡片</p>
                </div>
                
                <div class="feed-card should-be-visible">
                    <h4>推荐卡片 (.feed-card)</h4>
                    <p>这是推荐内容卡片</p>
                </div>
            </div>
        </div>
        
        <!-- B站广告元素测试 -->
        <div class="test-section">
            <h3>B站广告元素（应该被屏蔽）</h3>
            
            <div class="ad-report should-be-hidden">
                广告举报相关 (.ad-report)
            </div>
            
            <div class="advertisement should-be-hidden">
                广告容器 (.advertisement)
            </div>
            
            <div class="advertisement-banner should-be-hidden">
                广告横幅 (.advertisement-banner)
            </div>
            
            <div class="commercial-banner should-be-hidden">
                商业横幅 (.commercial-banner)
            </div>
            
            <div class="sponsor-banner should-be-hidden">
                赞助横幅 (.sponsor-banner)
            </div>
            
            <div class="ad-banner should-be-hidden">
                广告横幅 (.ad-banner)
            </div>
            
            <div class="ad-container should-be-hidden">
                广告容器 (.ad-container)
            </div>
            
            <div class="ad-wrapper should-be-hidden">
                广告包装器 (.ad-wrapper)
            </div>
            
            <div class="ad-card should-be-hidden">
                广告卡片 (.ad-card)
            </div>
        </div>
        
        <!-- 数据属性广告测试 -->
        <div class="test-section">
            <h3>数据属性广告（应该被屏蔽）</h3>
            
            <div data-report="ad-click" class="should-be-hidden">
                数据属性广告 ([data-report*="ad"])
            </div>
            
            <div data-ad-creative-type="banner" class="should-be-hidden">
                广告创意类型 ([data-ad-creative-type])
            </div>
            
            <div data-ad-slot="homepage-banner" class="should-be-hidden">
                广告位 ([data-ad-slot])
            </div>
            
            <div data-spm="ad.homepage.banner" class="should-be-hidden">
                SPM广告追踪 ([data-spm*="ad"])
            </div>
        </div>
        
        <!-- Eva广告系统测试 -->
        <div class="test-section">
            <h3>Eva广告系统（应该被屏蔽）</h3>
            
            <div class="eva-banner should-be-hidden" data-loc="ad-homepage">
                Eva广告横幅 (.eva-banner[data-loc*="ad"])
            </div>
            
            <div class="eva-extension should-be-hidden">
                Eva扩展广告 (.eva-extension)
            </div>
        </div>
        
        <!-- 信息流广告测试 -->
        <div class="test-section">
            <h3>信息流广告（应该被屏蔽）</h3>
            
            <div class="feed-ad-container should-be-hidden">
                信息流广告容器 (.feed-ad-container)
            </div>
            
            <div class="recommend-ad-card should-be-hidden">
                推荐广告卡片 (.recommend-ad-card)
            </div>
            
            <div class="live-ad-card should-be-hidden">
                直播广告卡片 (.live-ad-card)
            </div>
            
            <div class="pgc-ad-card should-be-hidden">
                PGC广告卡片 (.pgc-ad-card)
            </div>
        </div>
        
        <!-- 动态内容区域 -->
        <div class="test-section">
            <h3>动态内容测试</h3>
            <div id="dynamic-content">
                <p>点击"添加动态内容"按钮来测试动态加载的内容处理。</p>
            </div>
        </div>
    </div>
    
    <script>
        function checkStatus() {
            const shouldBeHidden = document.querySelectorAll('.should-be-hidden');
            const shouldBeVisible = document.querySelectorAll('.should-be-visible');
            
            let hiddenCount = 0;
            let visibleCount = 0;
            let falsePositives = 0;
            let falseNegatives = 0;
            
            // 检查应该被隐藏的元素
            shouldBeHidden.forEach(element => {
                const isHidden = window.getComputedStyle(element).display === 'none' ||
                               window.getComputedStyle(element).visibility === 'hidden' ||
                               window.getComputedStyle(element).opacity === '0';
                
                if (isHidden) {
                    hiddenCount++;
                } else {
                    falseNegatives++;
                }
            });
            
            // 检查应该保持可见的元素
            shouldBeVisible.forEach(element => {
                const isVisible = window.getComputedStyle(element).display !== 'none' &&
                                window.getComputedStyle(element).visibility !== 'hidden' &&
                                window.getComputedStyle(element).opacity !== '0';
                
                if (isVisible) {
                    visibleCount++;
                } else {
                    falsePositives++;
                }
            });
            
            const totalAds = shouldBeHidden.length;
            const totalNormal = shouldBeVisible.length;
            const blockRate = Math.round((hiddenCount / totalAds) * 100);
            const preserveRate = Math.round((visibleCount / totalNormal) * 100);
            
            document.getElementById('results').textContent = `
B站广告屏蔽测试结果：

📊 广告屏蔽效果：
   - 总广告元素: ${totalAds}
   - 成功屏蔽: ${hiddenCount}
   - 漏网之鱼: ${falseNegatives}
   - 屏蔽率: ${blockRate}%

🛡️ 正常内容保护：
   - 总正常元素: ${totalNormal}
   - 成功保护: ${visibleCount}
   - 误伤元素: ${falsePositives}
   - 保护率: ${preserveRate}%

🎯 总体评分：
   - 准确率: ${Math.round(((hiddenCount + visibleCount) / (totalAds + totalNormal)) * 100)}%
   - 误报率: ${Math.round((falsePositives / totalNormal) * 100)}%
   - 漏报率: ${Math.round((falseNegatives / totalAds) * 100)}%

${falsePositives === 0 && falseNegatives === 0 ? '🎉 完美！没有误伤和漏网！' : '⚠️ 需要进一步优化'}
            `;
        }
        
        function addDynamicContent() {
            const dynamicContent = document.getElementById('dynamic-content');
            
            const newContent = `
                <div class="video-card should-be-visible">动态添加的视频卡片</div>
                <div class="ad-card should-be-hidden">动态添加的广告卡片</div>
                <div class="bili-banner should-be-visible">动态添加的B站横幅</div>
                <div class="advertisement should-be-hidden">动态添加的广告</div>
            `;
            
            dynamicContent.innerHTML += newContent;
            
            setTimeout(() => {
                alert('动态内容已添加，请点击"检查屏蔽状态"查看效果');
            }, 1000);
        }
        
        function simulatePageLoad() {
            // 模拟B站页面加载过程
            const container = document.querySelector('.container');
            container.style.opacity = '0.5';
            
            setTimeout(() => {
                addDynamicContent();
                container.style.opacity = '1';
                alert('页面加载模拟完成');
            }, 500);
        }
        
        function showStats() {
            if (window.UAB_AdBlocker) {
                const stats = window.UAB_AdBlocker.getStats();
                document.getElementById('results').textContent = `
脚本运行统计：

🚫 屏蔽统计:
   - 屏蔽元素: ${stats.elementsBlocked || 0}
   - 拦截请求: ${stats.requestsBlocked || 0}
   - 运行时间: ${Math.round((stats.uptime || 0) / 1000)}秒
   - 每分钟屏蔽: ${stats.elementsPerMinute || 0}

📈 性能指标:
   - 脚本状态: 正常运行
   - B站优化: 已启用
   - 白名单保护: 已启用
                `;
            } else {
                document.getElementById('results').textContent = `
⚠️ 统计信息不可用

请确保：
1. 003.js脚本已正确加载
2. 启用了调试模式 (ENABLE_LOGGING: true)
3. 脚本正在运行中
                `;
            }
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            setTimeout(checkStatus, 2000);
        });
    </script>
</body>
</html>
