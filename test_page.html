<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Ad Blocker 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .should-be-hidden {
            background-color: #ffebee;
            color: #c62828;
            padding: 10px;
            margin: 10px 0;
            border: 2px solid #f44336;
            border-radius: 4px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.blocked {
            background-color: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .status.visible {
            background-color: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .controls {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #stats {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ Universal Ad Blocker 测试页面</h1>
        <p>这个页面包含各种模拟广告元素，用于测试003.js脚本的屏蔽效果。</p>
        
        <div class="controls">
            <h3>控制面板</h3>
            <button onclick="checkBlockingStatus()">检查屏蔽状态</button>
            <button onclick="showStats()">显示统计信息</button>
            <button onclick="testNetworkBlocking()">测试网络拦截</button>
            <button onclick="addDynamicAds()">添加动态广告</button>
        </div>
        
        <div id="stats"></div>
        
        <!-- 测试通用广告选择器 -->
        <div class="test-section">
            <h3>通用广告选择器测试</h3>
            
            <div class="ad-container should-be-hidden">
                <strong>应该被隐藏：</strong> .ad-container
            </div>
            
            <div class="advertisement should-be-hidden">
                <strong>应该被隐藏：</strong> .advertisement
            </div>
            
            <div class="banner should-be-hidden">
                <strong>应该被隐藏：</strong> .banner
            </div>
            
            <div class="sponsored should-be-hidden">
                <strong>应该被隐藏：</strong> .sponsored
            </div>
            
            <div id="ad-banner" class="should-be-hidden">
                <strong>应该被隐藏：</strong> #ad-banner
            </div>
            
            <div id="advertisement-top" class="should-be-hidden">
                <strong>应该被隐藏：</strong> #advertisement-top
            </div>
        </div>
        
        <!-- 测试Google Ads -->
        <div class="test-section">
            <h3>Google Ads 测试</h3>
            
            <div class="adsbygoogle should-be-hidden">
                <strong>应该被隐藏：</strong> .adsbygoogle (Google AdSense)
            </div>
            
            <div class="ad-slot should-be-hidden">
                <strong>应该被隐藏：</strong> .ad-slot
            </div>
            
            <div data-ad-slot="123456" class="should-be-hidden">
                <strong>应该被隐藏：</strong> [data-ad-slot]
            </div>
        </div>
        
        <!-- 测试社交媒体广告 -->
        <div class="test-section">
            <h3>社交媒体广告测试</h3>
            
            <div data-testid="ad-container" class="should-be-hidden">
                <strong>应该被隐藏：</strong> [data-testid*="ad"]
            </div>
            
            <div aria-label="Sponsored content" class="should-be-hidden">
                <strong>应该被隐藏：</strong> [aria-label*="Sponsored"]
            </div>
            
            <div class="promoted-post should-be-hidden">
                <strong>应该被隐藏：</strong> .promoted-post
            </div>
        </div>
        
        <!-- 测试弹窗和模态框 -->
        <div class="test-section">
            <h3>弹窗和模态框测试</h3>
            
            <div class="popup should-be-hidden">
                <strong>应该被隐藏：</strong> .popup
            </div>
            
            <div class="modal-ad should-be-hidden">
                <strong>应该被隐藏：</strong> .modal-ad
            </div>
            
            <div class="newsletter-popup should-be-hidden">
                <strong>应该被隐藏：</strong> .newsletter-popup
            </div>
            
            <div class="cookie-banner should-be-hidden">
                <strong>应该被隐藏：</strong> .cookie-banner
            </div>
        </div>
        
        <!-- 动态内容区域 -->
        <div class="test-section">
            <h3>动态内容测试</h3>
            <div id="dynamic-content">
                <p>点击"添加动态广告"按钮来测试动态添加的广告元素。</p>
            </div>
        </div>
        
        <!-- 正常内容（不应该被屏蔽） -->
        <div class="test-section">
            <h3>正常内容（不应该被屏蔽）</h3>
            
            <div class="content">
                <strong>应该可见：</strong> 正常内容区域
            </div>
            
            <div class="article">
                <strong>应该可见：</strong> 文章内容
            </div>
            
            <div class="navigation">
                <strong>应该可见：</strong> 导航菜单
            </div>
        </div>
    </div>
    
    <script>
        // 检查屏蔽状态
        function checkBlockingStatus() {
            const shouldBeHidden = document.querySelectorAll('.should-be-hidden');
            let hiddenCount = 0;
            let visibleCount = 0;
            
            shouldBeHidden.forEach(element => {
                const isHidden = window.getComputedStyle(element).display === 'none' ||
                               window.getComputedStyle(element).visibility === 'hidden' ||
                               window.getComputedStyle(element).opacity === '0';
                
                // 移除之前的状态类
                element.classList.remove('blocked', 'visible');
                
                if (isHidden) {
                    element.classList.add('blocked');
                    hiddenCount++;
                } else {
                    element.classList.add('visible');
                    visibleCount++;
                }
            });
            
            const statusDiv = document.getElementById('stats');
            statusDiv.innerHTML = `
                <h4>屏蔽状态检查结果：</h4>
                <p>✅ 已屏蔽: ${hiddenCount} 个元素</p>
                <p>⚠️ 仍可见: ${visibleCount} 个元素</p>
                <p>📊 屏蔽率: ${Math.round((hiddenCount / (hiddenCount + visibleCount)) * 100)}%</p>
            `;
        }
        
        // 显示统计信息
        function showStats() {
            const statsDiv = document.getElementById('stats');
            
            if (window.UAB_AdBlocker) {
                const stats = window.UAB_AdBlocker.getStats();
                statsDiv.innerHTML = `
                    <h4>脚本统计信息：</h4>
                    <p>🚫 屏蔽元素: ${stats.elementsBlocked}</p>
                    <p>🌐 拦截请求: ${stats.requestsBlocked}</p>
                    <p>⏱️ 运行时间: ${Math.round(stats.uptime / 1000)}秒</p>
                    <p>📈 每分钟屏蔽: ${stats.elementsPerMinute}</p>
                `;
            } else {
                statsDiv.innerHTML = `
                    <h4>统计信息不可用</h4>
                    <p>请确保003.js脚本已加载且启用了调试模式</p>
                `;
            }
        }
        
        // 测试网络拦截
        function testNetworkBlocking() {
            const testUrls = [
                'https://example.com/ads/banner.js',
                'https://doubleclick.net/ad.js',
                'https://googlesyndication.com/adsbygoogle.js',
                'https://facebook.com/tr?id=123'
            ];
            
            const statusDiv = document.getElementById('stats');
            statusDiv.innerHTML = '<h4>测试网络拦截...</h4>';
            
            let results = [];
            let completed = 0;
            
            testUrls.forEach(url => {
                fetch(url)
                    .then(() => {
                        results.push(`❌ ${url} - 未被拦截`);
                    })
                    .catch(() => {
                        results.push(`✅ ${url} - 已拦截`);
                    })
                    .finally(() => {
                        completed++;
                        if (completed === testUrls.length) {
                            statusDiv.innerHTML = `
                                <h4>网络拦截测试结果：</h4>
                                ${results.map(r => `<p>${r}</p>`).join('')}
                            `;
                        }
                    });
            });
        }
        
        // 添加动态广告
        function addDynamicAds() {
            const dynamicContent = document.getElementById('dynamic-content');
            
            const adElements = [
                '<div class="ad-banner should-be-hidden">动态添加的横幅广告</div>',
                '<div class="sponsored should-be-hidden">动态添加的赞助内容</div>',
                '<div data-ad-slot="dynamic" class="should-be-hidden">动态添加的广告位</div>',
                '<div class="popup should-be-hidden">动态添加的弹窗</div>'
            ];
            
            adElements.forEach(html => {
                dynamicContent.innerHTML += html;
            });
            
            // 等待一秒后检查状态
            setTimeout(() => {
                alert('动态广告已添加，请点击"检查屏蔽状态"查看效果');
            }, 1000);
        }
        
        // 页面加载完成后自动检查一次
        window.addEventListener('load', () => {
            setTimeout(checkBlockingStatus, 2000);
        });
    </script>
</body>
</html>
