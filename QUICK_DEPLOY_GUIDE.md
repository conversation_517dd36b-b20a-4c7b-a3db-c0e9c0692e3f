# 🚀 模块化广告屏蔽系统 - 快速部署指南

## 📋 部署清单

### ✅ 文件准备
确保您有以下三个脚本文件：
- [ ] `ad.js` - 通用全局广告屏蔽器
- [ ] `yt_ad.js` - YouTube专用广告屏蔽器  
- [ ] `bili_ad.js` - B站专用广告屏蔽器

### ✅ 环境要求
- [ ] Safari浏览器
- [ ] UserScript扩展（如Tampermonkey、Userscripts等）
- [ ] 管理员权限（用于安装扩展）

## 🎯 推荐部署方案

### 方案A：完整部署（推荐）
**适用于**: 经常使用YouTube、B站和其他网站的用户
**优势**: 全网覆盖，最佳体验

```bash
步骤：
1. 安装 ad.js      ← 覆盖所有通用网站
2. 安装 yt_ad.js   ← 专业YouTube体验
3. 安装 bili_ad.js ← 完美B站兼容
```

### 方案B：按需部署
**适用于**: 只使用特定网站的用户
**优势**: 最小化资源占用

```bash
选择安装：
- 主要用YouTube → 只安装 yt_ad.js
- 主要用B站 → 只安装 bili_ad.js
- 其他网站 → 只安装 ad.js
```

### 方案C：渐进部署
**适用于**: 想要逐步体验的用户
**优势**: 循序渐进，问题可控

```bash
阶段1: 安装 ad.js（基础覆盖）
阶段2: 发现YouTube问题时 → 添加 yt_ad.js
阶段3: 发现B站问题时 → 添加 bili_ad.js
```

## 📱 详细安装步骤

### 第一步：安装UserScript扩展
1. 打开Safari浏览器
2. 前往App Store搜索"Tampermonkey"或"Userscripts"
3. 安装并启用扩展
4. 在Safari设置中允许扩展运行

### 第二步：安装脚本文件

#### 安装 ad.js（通用广告屏蔽）
```javascript
1. 打开Tampermonkey管理面板
2. 点击"创建新脚本"
3. 复制粘贴 ad.js 的完整内容
4. 保存脚本
5. 确保脚本已启用
```

#### 安装 yt_ad.js（YouTube专用）
```javascript
1. 重复上述步骤
2. 复制粘贴 yt_ad.js 的完整内容
3. 保存并启用
```

#### 安装 bili_ad.js（B站专用）
```javascript
1. 重复上述步骤
2. 复制粘贴 bili_ad.js 的完整内容
3. 保存并启用
```

### 第三步：验证安装

#### 验证通用广告屏蔽（ad.js）
```bash
1. 访问任意新闻网站（如CNN、BBC）
2. 检查横幅广告是否被屏蔽
3. 访问社交媒体（如Facebook、Twitter）
4. 确认赞助内容被过滤
```

#### 验证YouTube广告屏蔽（yt_ad.js）
```bash
1. 访问 youtube.com
2. 播放任意视频
3. 确认视频广告被跳过
4. 检查页面广告是否被移除
```

#### 验证B站广告屏蔽（bili_ad.js）
```bash
1. 访问 bilibili.com
2. 检查首页轮播图是否正常显示
3. 确认视频卡片信息完整
4. 验证广告被屏蔽但UI无损
```

## 🔧 配置优化

### 性能优化配置
根据您的设备性能调整以下参数：

#### 高性能设备
```javascript
// 在每个脚本中修改CONFIG对象
OBSERVER_THROTTLE: 100,    // 更快响应
BATCH_SIZE: 60,            // 更大批处理
CLEANUP_INTERVAL: 5000     // 更频繁清理
```

#### 标准设备（默认）
```javascript
OBSERVER_THROTTLE: 200,    // 标准响应
BATCH_SIZE: 40,            // 标准批处理
CLEANUP_INTERVAL: 8000     // 标准清理
```

#### 低性能设备
```javascript
OBSERVER_THROTTLE: 400,    // 较慢响应
BATCH_SIZE: 20,            // 较小批处理
CLEANUP_INTERVAL: 15000    // 较少清理
```

### 调试模式配置
如需调试或查看详细信息：
```javascript
// 在脚本中修改
ENABLE_LOGGING: true,              // 开启日志
ENABLE_PERFORMANCE_MONITORING: true // 开启监控
```

## 🚨 故障排除

### 常见问题及解决方案

#### 问题1：脚本不生效
```bash
解决步骤：
1. 检查UserScript扩展是否启用
2. 确认脚本在管理面板中显示为"启用"
3. 刷新目标网页
4. 检查浏览器控制台是否有错误信息
```

#### 问题2：YouTube视频无法播放
```bash
解决步骤：
1. 暂时禁用 yt_ad.js
2. 刷新YouTube页面
3. 如果恢复正常，调整yt_ad.js的配置参数
4. 重新启用脚本
```

#### 问题3：B站UI显示异常
```bash
解决步骤：
1. 确认只安装了 bili_ad.js，没有其他B站相关脚本
2. 清除浏览器缓存
3. 刷新B站页面
4. 检查bili_ad.js的保护选择器配置
```

#### 问题4：网页加载变慢
```bash
解决步骤：
1. 调整OBSERVER_THROTTLE参数（增加数值）
2. 减少BATCH_SIZE参数
3. 增加CLEANUP_INTERVAL参数
4. 关闭不必要的性能监控
```

## 📊 效果验证

### 验证工具
使用以下方法验证屏蔽效果：

#### 方法1：控制台检查
```javascript
// 在浏览器控制台执行
console.log('Ad elements found:', document.querySelectorAll('[class*="ad-"]').length);
console.log('Hidden elements:', document.querySelectorAll('[style*="display: none"]').length);
```

#### 方法2：性能统计
```javascript
// 如果启用了调试模式
if (window.UAB_AdBlocker) {
    console.log('Stats:', window.UAB_AdBlocker.getStats());
}
if (window.BILI_AdBlocker) {
    console.log('Bilibili Stats:', window.BILI_AdBlocker.getStats());
}
```

#### 方法3：网络监控
1. 打开浏览器开发者工具
2. 切换到"网络"标签
3. 刷新页面
4. 查看被阻止的请求（显示为红色或失败状态）

## 🎯 最佳实践

### 1. 定期更新
```bash
建议每月检查一次：
- 脚本是否有新版本
- 广告屏蔽效果是否正常
- 是否需要调整配置参数
```

### 2. 备份配置
```bash
重要提醒：
- 保存您的自定义配置
- 记录性能优化参数
- 备份脚本文件以防丢失
```

### 3. 监控性能
```bash
定期检查：
- 浏览器内存使用情况
- 页面加载速度
- CPU占用率
```

## 🎉 部署完成

恭喜！您已经成功部署了模块化广告屏蔽系统。现在您可以享受：

✅ **无广告浏览体验** - 全网广告被有效屏蔽
✅ **优化的性能表现** - 每个网站只加载必要代码
✅ **完美的UI兼容性** - 特别是YouTube和B站
✅ **智能的反检测** - 避免被网站检测到广告屏蔽

如果遇到任何问题，请参考故障排除部分或查看详细的架构文档。祝您浏览愉快！🎊
